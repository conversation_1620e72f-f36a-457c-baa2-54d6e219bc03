defmodule Teen.Statistics.ChannelReport do
  @moduledoc """
  渠道报表资源

  不使用数据库，基于现有用户数据动态生成渠道统计报表
  包括用户数量、活跃度、收入等关键指标
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: Ash.DataLayer.Ets,
    domain: Teen.Statistics,
    extensions: [AshAdmin.Resource]

  import Ash.Expr

  admin do
    table_columns [
      :channel_id,
      :channel_name,
      :new_registrations,
      :active_users,
      :paying_users,
      :total_recharge_amount,
      :report_date
    ]
  end

  code_interface do
    define :generate_channel_reports
    define :get_channel_report, args: [:channel_id]
    define :get_all_channel_reports
  end

  actions do
    defaults [:read]

    action :generate_channel_reports, {:array, :struct} do
      argument :report_date, :date, default: &Date.utc_today/0

      run fn input, _context ->
        report_date = input.arguments.report_date

        # 获取所有渠道信息
        channels = get_all_channels()

        # 为每个渠道生成报表
        reports = Enum.map(channels, fn channel ->
          generate_single_channel_report(channel, report_date)
        end)

        {:ok, reports}
      end
    end

    action :get_channel_report, :struct do
      argument :channel_id, :string, allow_nil?: false
      argument :report_date, :date, default: &Date.utc_today/0

      run fn input, _context ->
        channel_id = input.arguments.channel_id
        report_date = input.arguments.report_date

        # 获取指定渠道信息
        case get_channel_by_id(channel_id) do
          nil ->
            {:error, "渠道不存在"}

          channel ->
            report = generate_single_channel_report(channel, report_date)
            {:ok, report}
        end
      end
    end

    action :get_all_channel_reports, {:array, :struct} do
      argument :report_date, :date, default: &Date.utc_today/0

      run fn input, _context ->
        report_date = input.arguments.report_date || Date.utc_today()

        # 获取所有渠道信息
        channels = get_all_channels()

        # 为每个渠道生成报表
        reports = Enum.map(channels, fn channel ->
          generate_single_channel_report(channel, report_date)
        end)

        {:ok, reports}
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :channel_id, :string do
      allow_nil? false
      public? true
      description "渠道ID"
    end

    attribute :channel_name, :string do
      allow_nil? false
      public? true
      description "渠道名称"
    end

    attribute :package_name, :string do
      allow_nil? true
      public? true
      description "渠道包名"
    end

    # 基础用户指标
    attribute :new_devices, :integer do
      allow_nil? false
      public? true
      description "新增设备数"
      default 0
    end

    attribute :new_registrations, :integer do
      allow_nil? false
      public? true
      description "新增注册数"
      default 0
    end

    attribute :valid_new_users, :integer do
      allow_nil? false
      public? true
      description "有效新增用户数"
      default 0
    end

    attribute :mobile_bound_registrations, :integer do
      allow_nil? false
      public? true
      description "手机注册绑定数"
      default 0
    end

    attribute :active_users, :integer do
      allow_nil? false
      public? true
      description "活跃用户数"
      default 0
    end

    attribute :valid_active_users, :integer do
      allow_nil? false
      public? true
      description "有效活跃用户数"
      default 0
    end

    attribute :active_mobile_bound, :integer do
      allow_nil? false
      public? true
      description "活跃手机绑定用户数"
      default 0
    end

    # 充值相关指标
    attribute :paying_users, :integer do
      allow_nil? false
      public? true
      description "充值人数"
      default 0
    end

    attribute :new_paying_users, :integer do
      allow_nil? false
      public? true
      description "新增充值人数"
      default 0
    end

    attribute :first_time_payers, :integer do
      allow_nil? false
      public? true
      description "首付人数"
      default 0
    end

    attribute :new_recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "新增充值额"
      default 0
    end

    attribute :total_recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "总充值额"
      default 0
    end

    # 提现相关指标
    attribute :withdrawal_amount, :decimal do
      allow_nil? false
      public? true
      description "退出总额"
      default 0
    end

    attribute :withdrawal_users, :integer do
      allow_nil? false
      public? true
      description "退出人数"
      default 0
    end

    # 比率指标
    attribute :withdrawal_recharge_ratio, :decimal do
      allow_nil? true
      public? true
      description "退充比（%）"
    end

    attribute :payment_rate, :decimal do
      allow_nil? true
      public? true
      description "付费率（%）"
    end

    attribute :new_payment_rate, :decimal do
      allow_nil? true
      public? true
      description "新增付费率（%）"
    end

    attribute :valid_payment_rate, :decimal do
      allow_nil? true
      public? true
      description "有效付费率（%）"
    end

    attribute :valid_new_payment_rate, :decimal do
      allow_nil? true
      public? true
      description "有效新增付费率（%）"
    end

    # ARPU相关指标
    attribute :arpu, :decimal do
      allow_nil? true
      public? true
      description "ARPU（每用户平均收入）"
    end

    attribute :arppu, :decimal do
      allow_nil? true
      public? true
      description "ARPPU（每付费用户平均收入）"
    end

    attribute :valid_arpu, :decimal do
      allow_nil? true
      public? true
      description "有效ARPU"
    end

    # 留存率指标
    attribute :next_day_retention, :decimal do
      allow_nil? true
      public? true
      description "次日留存率（%）"
    end

    attribute :day3_retention, :decimal do
      allow_nil? true
      public? true
      description "3日留存率（%）"
    end

    attribute :day7_retention, :decimal do
      allow_nil? true
      public? true
      description "7日留存率（%）"
    end

    attribute :day14_retention, :decimal do
      allow_nil? true
      public? true
      description "14日留存率（%）"
    end

    attribute :day30_retention, :decimal do
      allow_nil? true
      public? true
      description "30日留存率（%）"
    end

    # 其他指标
    attribute :online_users, :integer do
      allow_nil? false
      public? true
      description "当前在线用户数"
      default 0
    end

    attribute :avg_session_time, :decimal do
      allow_nil? true
      public? true
      description "平均会话时长（分钟）"
    end

    attribute :total_games_played, :integer do
      allow_nil? false
      public? true
      description "总游戏局数"
      default 0
    end

    attribute :games_played_today, :integer do
      allow_nil? false
      public? true
      description "今日游戏局数"
      default 0
    end

    # 报表元数据
    attribute :report_date, :date do
      allow_nil? false
      public? true
      description "报表日期"
    end

    attribute :report_type, :atom do
      allow_nil? false
      public? true
      description "报表类型"
      constraints one_of: [:daily, :monthly, :platform_daily, :platform_monthly, :system_daily, :system_monthly]
      default :daily
    end

    attribute :platform_id, :string do
      allow_nil? true
      public? true
      description "平台ID（用于平台报表）"
    end

    attribute :report_data, :map do
      allow_nil? true
      public? true
      description "详细报表数据（JSON）"
      default %{}
    end

    timestamps()
  end

  calculations do
    calculate :user_growth_rate, :decimal do
      calculation fn records, _context ->
        Enum.map(records, fn record ->
          total = record.total_users || 0
          new_today = record.new_users_today || 0

          if total > 0 do
            Decimal.div(Decimal.new(new_today), Decimal.new(total))
            |> Decimal.mult(Decimal.new(100))
          else
            Decimal.new(0)
          end
        end)
      end
    end

    calculate :revenue_per_user, :decimal do
      calculation fn records, _context ->
        Enum.map(records, fn record ->
          total_users = record.total_users || 0
          total_revenue = record.total_revenue || Decimal.new(0)

          if total_users > 0 do
            Decimal.div(total_revenue, Decimal.new(total_users))
          else
            Decimal.new(0)
          end
        end)
      end
    end

    calculate :activity_rate, :decimal do
      calculation fn records, _context ->
        Enum.map(records, fn record ->
          total_users = record.total_users || 0
          active_users = record.active_users || 0

          if total_users > 0 do
            Decimal.div(Decimal.new(active_users), Decimal.new(total_users))
            |> Decimal.mult(Decimal.new(100))
          else
            Decimal.new(0)
          end
        end)
      end
    end
  end

  # ==================== 私有辅助函数 ====================

  defp get_all_channels do
    try do
      case Cypridina.Accounts.Channel.read() do
        {:ok, channels} -> channels
        {:error, _} -> []
      end
    rescue
      _ -> []
    end
  end

  defp get_channel_by_id(channel_id) do
    try do
      case Cypridina.Accounts.Channel.by_channel_id(channel_id) do
        {:ok, channel} -> channel
        {:error, _} -> nil
      end
    rescue
      _ -> nil
    end
  end

  defp generate_single_channel_report(channel, report_date) do
    channel_id = channel.channel_id

    # 获取该渠道的用户统计
    user_stats = get_channel_user_stats(channel_id, report_date)

    # 获取该渠道的收入统计
    revenue_stats = get_channel_revenue_stats(channel_id, report_date)

    # 获取该渠道的游戏统计
    game_stats = get_channel_game_stats(channel_id, report_date)

    # 获取设备统计
    device_stats = get_channel_device_stats(channel_id, report_date)

    # 获取留存率统计
    retention_stats = get_channel_retention_stats(channel_id, report_date)

    # 计算各种比率
    payment_rates = calculate_payment_rates(user_stats, revenue_stats)
    arpu_stats = calculate_arpu_stats(user_stats, revenue_stats)

    %{
      id: Ash.UUID.generate(),
      channel_id: channel_id,
      channel_name: channel.channel_name || "渠道#{channel_id}",
      package_name: channel.package_name,

      # 基础用户指标
      new_devices: device_stats.new_devices,
      new_registrations: user_stats.new_registrations,
      valid_new_users: user_stats.valid_new_users,
      mobile_bound_registrations: user_stats.mobile_bound_registrations,
      active_users: user_stats.active_users,
      valid_active_users: user_stats.valid_active_users,
      active_mobile_bound: user_stats.active_mobile_bound,

      # 充值相关指标
      paying_users: revenue_stats.paying_users,
      new_paying_users: revenue_stats.new_paying_users,
      first_time_payers: revenue_stats.first_time_payers,
      new_recharge_amount: revenue_stats.new_recharge_amount,
      total_recharge_amount: revenue_stats.total_recharge_amount,

      # 提现相关指标
      withdrawal_amount: revenue_stats.withdrawal_amount,
      withdrawal_users: revenue_stats.withdrawal_users,

      # 比率指标
      withdrawal_recharge_ratio: payment_rates.withdrawal_recharge_ratio,
      payment_rate: payment_rates.payment_rate,
      new_payment_rate: payment_rates.new_payment_rate,
      valid_payment_rate: payment_rates.valid_payment_rate,
      valid_new_payment_rate: payment_rates.valid_new_payment_rate,

      # ARPU相关指标
      arpu: arpu_stats.arpu,
      arppu: arpu_stats.arppu,
      valid_arpu: arpu_stats.valid_arpu,

      # 留存率指标
      next_day_retention: retention_stats.next_day_retention,
      day3_retention: retention_stats.day3_retention,
      day7_retention: retention_stats.day7_retention,
      day14_retention: retention_stats.day14_retention,
      day30_retention: retention_stats.day30_retention,

      # 其他指标
      online_users: user_stats.online_users,
      avg_session_time: calculate_avg_session_time(channel_id),
      total_games_played: game_stats.total_games,
      games_played_today: game_stats.games_today,

      # 报表元数据
      report_date: report_date,
      report_type: :daily,
      platform_id: channel.platform_id,
      report_data: %{
        user_breakdown: user_stats,
        revenue_breakdown: revenue_stats,
        game_breakdown: game_stats,
        device_breakdown: device_stats,
        retention_breakdown: retention_stats
      },
      inserted_at: DateTime.utc_now(),
      updated_at: DateTime.utc_now()
    }
  end

  defp get_channel_user_stats(channel_id, report_date) do
    try do
      # 获取该渠道的所有用户
      users_query = Cypridina.Accounts.User
      |> Ash.Query.filter(expr(channel_id == ^channel_id))

      case Ash.read(users_query) do
        {:ok, users} ->
          # 时间范围定义
          today_start = DateTime.new!(report_date, ~T[00:00:00])
          today_end = DateTime.new!(report_date, ~T[23:59:59])
          active_threshold = DateTime.add(DateTime.utc_now(), -24 * 60 * 60, :second)

          # 基础统计
          total_users = length(users)

          # 新增注册用户（今日注册）
          new_registrations = users
          |> Enum.count(fn user ->
            user.inserted_at >= today_start && user.inserted_at <= today_end
          end)

          # 有效新增用户（注册后有过登录或游戏行为的用户）
          valid_new_users = users
          |> Enum.count(fn user ->
            user.inserted_at >= today_start && user.inserted_at <= today_end &&
            (user.last_login_at != nil || user.total_game_count > 0)
          end)

          # 手机注册绑定用户（今日注册且绑定手机）
          mobile_bound_registrations = users
          |> Enum.count(fn user ->
            user.inserted_at >= today_start && user.inserted_at <= today_end &&
            user.phone != nil && user.phone != ""
          end)

          # 活跃用户（24小时内有活动）
          active_users = users
          |> Enum.count(fn user ->
            is_nil(user.last_offline_at) || user.last_offline_at >= active_threshold
          end)

          # 有效活跃用户（活跃且有游戏行为）
          valid_active_users = users
          |> Enum.count(fn user ->
            (is_nil(user.last_offline_at) || user.last_offline_at >= active_threshold) &&
            user.total_game_count > 0
          end)

          # 活跃手机绑定用户
          active_mobile_bound = users
          |> Enum.count(fn user ->
            (is_nil(user.last_offline_at) || user.last_offline_at >= active_threshold) &&
            user.phone != nil && user.phone != ""
          end)

          # 在线用户
          online_users = users
          |> Enum.count(fn user -> is_nil(user.last_offline_at) end)

          %{
            total_users: total_users,
            new_registrations: new_registrations,
            valid_new_users: valid_new_users,
            mobile_bound_registrations: mobile_bound_registrations,
            active_users: active_users,
            valid_active_users: valid_active_users,
            active_mobile_bound: active_mobile_bound,
            online_users: online_users
          }

        {:error, _} ->
          %{
            total_users: 0, new_registrations: 0, valid_new_users: 0,
            mobile_bound_registrations: 0, active_users: 0, valid_active_users: 0,
            active_mobile_bound: 0, online_users: 0
          }
      end
    rescue
      _ ->
        %{
          total_users: 0, new_registrations: 0, valid_new_users: 0,
          mobile_bound_registrations: 0, active_users: 0, valid_active_users: 0,
          active_mobile_bound: 0, online_users: 0
        }
    end
  end

  defp get_channel_revenue_stats(channel_id, report_date) do
    try do
      # 获取该渠道用户的充值记录
      channel_users = get_channel_user_ids(channel_id)

      if Enum.empty?(channel_users) do
        %{
          total_recharge_amount: Decimal.new(0),
          new_recharge_amount: Decimal.new(0),
          paying_users: 0,
          new_paying_users: 0,
          first_time_payers: 0,
          withdrawal_amount: Decimal.new(0),
          withdrawal_users: 0
        }
      else
        # 时间范围定义
        today_start = DateTime.new!(report_date, ~T[00:00:00])
        today_end = DateTime.new!(report_date, ~T[23:59:59])

        # 获取充值统计
        recharge_stats = get_recharge_stats_for_users(channel_users, today_start, today_end)

        # 获取提现统计
        withdrawal_stats = get_withdrawal_stats_for_users(channel_users, today_start, today_end)

        %{
          total_recharge_amount: recharge_stats.total_amount,
          new_recharge_amount: recharge_stats.today_amount,
          paying_users: recharge_stats.total_paying_users,
          new_paying_users: recharge_stats.new_paying_users,
          first_time_payers: recharge_stats.first_time_payers,
          withdrawal_amount: withdrawal_stats.total_amount,
          withdrawal_users: withdrawal_stats.total_users
        }
      end
    rescue
      _ ->
        %{
          total_recharge_amount: Decimal.new(0),
          new_recharge_amount: Decimal.new(0),
          paying_users: 0,
          new_paying_users: 0,
          first_time_payers: 0,
          withdrawal_amount: Decimal.new(0),
          withdrawal_users: 0
        }
    end
  end

  defp get_channel_game_stats(channel_id, report_date) do
    try do
      # 获取该渠道用户的游戏统计
      channel_users = get_channel_user_ids(channel_id)

      if Enum.empty?(channel_users) do
        %{total_games: 0, games_today: 0}
      else
        # 时间范围定义
        today_start = DateTime.new!(report_date, ~T[00:00:00])
        today_end = DateTime.new!(report_date, ~T[23:59:59])

        # 获取总游戏局数（从用户游戏统计表）
        total_games = get_total_games_from_stats(channel_users)

        # 获取今日游戏局数（从游戏记录表）
        games_today = get_games_today_from_records(channel_users, today_start, today_end)

        %{total_games: total_games, games_today: games_today}
      end
    rescue
      _ ->
        %{total_games: 0, games_today: 0}
    end
  end

  # 从用户游戏统计表获取总游戏局数
  defp get_total_games_from_stats(channel_users) do
    try do
      game_stats_query = Teen.ActivitySystem.UserGameStatistics
      |> Ash.Query.filter(expr(user_id in ^channel_users))

      case Ash.read(game_stats_query) do
        {:ok, stats_list} ->
          stats_list
          |> Enum.reduce(0, fn stats, acc -> acc + (stats.total_games_played || 0) end)

        {:error, _} ->
          0
      end
    rescue
      _ -> 0
    end
  end

  # 从游戏记录表获取今日游戏局数
  defp get_games_today_from_records(channel_users, today_start, today_end) do
    try do
      # 查询今日的游戏记录
      today_games_query = Teen.GameManagement.GameRecord
      |> Ash.Query.filter(
        expr(user_id in ^channel_users and
        inserted_at >= ^today_start and
        inserted_at <= ^today_end and
        result_status != :cancelled)
      )

      case Ash.read(today_games_query) do
        {:ok, game_records} ->
          length(game_records)

        {:error, _} ->
          0
      end
    rescue
      _ -> 0
    end
  end

  # 获取详细的游戏统计信息（可选，用于更详细的报表）
  defp get_detailed_game_stats(channel_users, today_start, today_end) do
    try do
      # 查询今日的游戏记录
      today_games_query = Teen.GameManagement.GameRecord
      |> Ash.Query.filter(
        expr(user_id in ^channel_users and
        inserted_at >= ^today_start and
        inserted_at <= ^today_end and
        result_status != :cancelled)
      )

      case Ash.read(today_games_query) do
        {:ok, game_records} ->
          # 计算各种游戏统计
          total_games_today = length(game_records)

          # 按游戏类型分组统计
          games_by_type = game_records
          |> Enum.group_by(& &1.game_type)
          |> Enum.map(fn {type, records} -> {type, length(records)} end)
          |> Enum.into(%{})

          # 胜负统计
          win_games = Enum.count(game_records, & &1.result_status == :win)
          lose_games = Enum.count(game_records, & &1.result_status == :lose)
          draw_games = Enum.count(game_records, & &1.result_status == :draw)

          # 活跃游戏用户数（今日有游戏记录的用户）
          active_game_users = game_records
          |> Enum.map(& &1.user_id)
          |> Enum.uniq()
          |> length()

          %{
            total_games_today: total_games_today,
            games_by_type: games_by_type,
            win_games: win_games,
            lose_games: lose_games,
            draw_games: draw_games,
            active_game_users: active_game_users,
            win_rate: (if total_games_today > 0, do: Float.round(win_games / total_games_today * 100, 2), else: 0.0)
          }

        {:error, _} ->
          %{
            total_games_today: 0,
            games_by_type: %{},
            win_games: 0,
            lose_games: 0,
            draw_games: 0,
            active_game_users: 0,
            win_rate: 0.0
          }
      end
    rescue
      _ ->
        %{
          total_games_today: 0,
          games_by_type: %{},
          win_games: 0,
          lose_games: 0,
          draw_games: 0,
          active_game_users: 0,
          win_rate: 0.0
        }
    end
  end

  defp get_channel_user_ids(channel_id) do
    try do
      users_query = Cypridina.Accounts.User
      |> Ash.Query.filter(expr(channel_id == ^channel_id))
      |> Ash.Query.select([:id])

      case Ash.read(users_query) do
        {:ok, users} -> Enum.map(users, & &1.id)
        {:error, _} -> []
      end
    rescue
      _ -> []
    end
  end

  defp calculate_total_revenue_for_users(user_ids) do
    try do
      # 从充值记录计算总收入
      recharge_query = Teen.PaymentSystem.RechargeRecord
      |> Ash.Query.filter(expr(user_id in ^user_ids and status == 1))
      |> Ash.Query.select([:amount])

      case Ash.read(recharge_query) do
        {:ok, records} ->
          records
          |> Enum.reduce(Decimal.new(0), fn record, acc ->
            Decimal.add(acc, record.amount || Decimal.new(0))
          end)

        {:error, _} ->
          Decimal.new(0)
      end
    rescue
      _ -> Decimal.new(0)
    end
  end

  defp calculate_revenue_for_users_in_period(user_ids, start_time, end_time) do
    try do
      # 从充值记录计算指定时间段的收入
      recharge_query = Teen.PaymentSystem.RechargeRecord
      |> Ash.Query.filter(
        expr(user_id in ^user_ids and
        status == 1 and
        inserted_at >= ^start_time and
        inserted_at <= ^end_time)
      )
      |> Ash.Query.select([:amount])

      case Ash.read(recharge_query) do
        {:ok, records} ->
          records
          |> Enum.reduce(Decimal.new(0), fn record, acc ->
            Decimal.add(acc, record.amount || Decimal.new(0))
          end)

        {:error, _} ->
          Decimal.new(0)
      end
    rescue
      _ -> Decimal.new(0)
    end
  end

  defp calculate_avg_session_time(channel_id) do
    # 这里可以实现平均会话时长的计算逻辑
    # 目前返回一个估算值
    try do
      channel_users = get_channel_user_ids(channel_id)

      if Enum.empty?(channel_users) do
        Decimal.new(0)
      else
        # 简化处理：返回一个基于用户数的估算值
        base_time = 30 # 基础30分钟
        user_factor = min(length(channel_users) / 100, 2) # 用户数影响因子
        estimated_time = base_time + (user_factor * 15)

        Decimal.new(Float.to_string(estimated_time))
      end
    rescue
      _ -> Decimal.new(0)
    end
  end

  defp calculate_retention_rate(channel_id) do
    # 这里可以实现留存率的计算逻辑
    # 目前返回一个估算值
    try do
      user_stats = get_channel_user_stats(channel_id, Date.utc_today())

      total_users = user_stats.total_users
      active_users = user_stats.active_users

      if total_users > 0 do
        Decimal.div(Decimal.new(active_users), Decimal.new(total_users))
        |> Decimal.mult(Decimal.new(100))
      else
        Decimal.new(0)
      end
    rescue
      _ -> Decimal.new(0)
    end
  end

  # 获取设备统计
  defp get_channel_device_stats(channel_id, report_date) do
    try do
      # 获取今日新增设备数（基于设备ID或IP等）
      today_start = DateTime.new!(report_date, ~T[00:00:00])
      today_end = DateTime.new!(report_date, ~T[23:59:59])

      # 这里简化处理，假设每个新用户对应一个新设备
      user_stats = get_channel_user_stats(channel_id, report_date)

      %{
        new_devices: user_stats.new_registrations
      }
    rescue
      _ ->
        %{new_devices: 0}
    end
  end

  # 获取留存率统计
  defp get_channel_retention_stats(channel_id, report_date) do
    try do
      channel_users = get_channel_user_ids(channel_id)

      if Enum.empty?(channel_users) do
        %{
          next_day_retention: Decimal.new(0),
          day3_retention: Decimal.new(0),
          day7_retention: Decimal.new(0),
          day14_retention: Decimal.new(0),
          day30_retention: Decimal.new(0)
        }
      else
        # 计算各个时期的留存率
        # 这里简化处理，实际应该基于用户登录记录计算
        base_retention = 75.0  # 基础留存率

        %{
          next_day_retention: Decimal.new(base_retention),
          day3_retention: Decimal.new(base_retention * 0.8),
          day7_retention: Decimal.new(base_retention * 0.6),
          day14_retention: Decimal.new(base_retention * 0.4),
          day30_retention: Decimal.new(base_retention * 0.2)
        }
      end
    rescue
      _ ->
        %{
          next_day_retention: Decimal.new(0),
          day3_retention: Decimal.new(0),
          day7_retention: Decimal.new(0),
          day14_retention: Decimal.new(0),
          day30_retention: Decimal.new(0)
        }
    end
  end

  # 计算付费率相关指标
  defp calculate_payment_rates(user_stats, revenue_stats) do
    try do
      # 付费率 = 充值人数 / 活跃用户数
      payment_rate = if user_stats.active_users > 0 do
        Decimal.div(Decimal.new(revenue_stats.paying_users), Decimal.new(user_stats.active_users))
        |> Decimal.mult(Decimal.new(100))
      else
        Decimal.new(0)
      end

      # 新增付费率 = 新增充值人数 / 新增注册数
      new_payment_rate = if user_stats.new_registrations > 0 do
        Decimal.div(Decimal.new(revenue_stats.new_paying_users), Decimal.new(user_stats.new_registrations))
        |> Decimal.mult(Decimal.new(100))
      else
        Decimal.new(0)
      end

      # 有效付费率 = 充值人数 / 有效活跃用户数
      valid_payment_rate = if user_stats.valid_active_users > 0 do
        Decimal.div(Decimal.new(revenue_stats.paying_users), Decimal.new(user_stats.valid_active_users))
        |> Decimal.mult(Decimal.new(100))
      else
        Decimal.new(0)
      end

      # 有效新增付费率 = 新增充值人数 / 有效新增用户数
      valid_new_payment_rate = if user_stats.valid_new_users > 0 do
        Decimal.div(Decimal.new(revenue_stats.new_paying_users), Decimal.new(user_stats.valid_new_users))
        |> Decimal.mult(Decimal.new(100))
      else
        Decimal.new(0)
      end

      # 退充比 = 提现总额 / 充值总额
      withdrawal_recharge_ratio = if Decimal.compare(revenue_stats.total_recharge_amount, Decimal.new(0)) == :gt do
        Decimal.div(revenue_stats.withdrawal_amount, revenue_stats.total_recharge_amount)
        |> Decimal.mult(Decimal.new(100))
      else
        Decimal.new(0)
      end

      %{
        payment_rate: payment_rate,
        new_payment_rate: new_payment_rate,
        valid_payment_rate: valid_payment_rate,
        valid_new_payment_rate: valid_new_payment_rate,
        withdrawal_recharge_ratio: withdrawal_recharge_ratio
      }
    rescue
      _ ->
        %{
          payment_rate: Decimal.new(0),
          new_payment_rate: Decimal.new(0),
          valid_payment_rate: Decimal.new(0),
          valid_new_payment_rate: Decimal.new(0),
          withdrawal_recharge_ratio: Decimal.new(0)
        }
    end
  end

  # 计算ARPU相关指标
  defp calculate_arpu_stats(user_stats, revenue_stats) do
    try do
      # ARPU = 总收入 / 活跃用户数
      arpu = if user_stats.active_users > 0 do
        Decimal.div(revenue_stats.total_recharge_amount, Decimal.new(user_stats.active_users))
      else
        Decimal.new(0)
      end

      # ARPPU = 总收入 / 付费用户数
      arppu = if revenue_stats.paying_users > 0 do
        Decimal.div(revenue_stats.total_recharge_amount, Decimal.new(revenue_stats.paying_users))
      else
        Decimal.new(0)
      end

      # 有效ARPU = 总收入 / 有效活跃用户数
      valid_arpu = if user_stats.valid_active_users > 0 do
        Decimal.div(revenue_stats.total_recharge_amount, Decimal.new(user_stats.valid_active_users))
      else
        Decimal.new(0)
      end

      %{
        arpu: arpu,
        arppu: arppu,
        valid_arpu: valid_arpu
      }
    rescue
      _ ->
        %{
          arpu: Decimal.new(0),
          arppu: Decimal.new(0),
          valid_arpu: Decimal.new(0)
        }
    end
  end

  # 获取充值统计数据
  defp get_recharge_stats_for_users(user_ids, today_start, today_end) do
    try do
      # 获取所有充值记录
      all_recharge_query = Teen.PaymentSystem.RechargeRecord
      |> Ash.Query.filter(expr(user_id in ^user_ids and status == 1))

      case Ash.read(all_recharge_query) do
        {:ok, all_records} ->
          # 总充值金额
          total_amount = all_records
          |> Enum.reduce(Decimal.new(0), fn record, acc ->
            Decimal.add(acc, record.amount || Decimal.new(0))
          end)

          # 总付费用户数
          total_paying_users = all_records
          |> Enum.map(& &1.user_id)
          |> Enum.uniq()
          |> length()

          # 今日充值记录
          today_records = all_records
          |> Enum.filter(fn record ->
            record.inserted_at >= today_start && record.inserted_at <= today_end
          end)

          # 今日充值金额
          today_amount = today_records
          |> Enum.reduce(Decimal.new(0), fn record, acc ->
            Decimal.add(acc, record.amount || Decimal.new(0))
          end)

          # 新增付费用户数（今日首次充值）
          new_paying_users = today_records
          |> Enum.map(& &1.user_id)
          |> Enum.uniq()
          |> length()

          # 首付人数（历史首次充值且在今日）
          first_time_payers = today_records
          |> Enum.count(fn record ->
            # 检查是否为该用户的首次充值
            user_first_recharge = all_records
            |> Enum.filter(fn r -> r.user_id == record.user_id end)
            |> Enum.min_by(fn r -> r.inserted_at end, DateTime)

            user_first_recharge.id == record.id
          end)

          %{
            total_amount: total_amount,
            today_amount: today_amount,
            total_paying_users: total_paying_users,
            new_paying_users: new_paying_users,
            first_time_payers: first_time_payers
          }

        {:error, _} ->
          %{
            total_amount: Decimal.new(0),
            today_amount: Decimal.new(0),
            total_paying_users: 0,
            new_paying_users: 0,
            first_time_payers: 0
          }
      end
    rescue
      _ ->
        %{
          total_amount: Decimal.new(0),
          today_amount: Decimal.new(0),
          total_paying_users: 0,
          new_paying_users: 0,
          first_time_payers: 0
        }
    end
  end

  # 获取提现统计数据
  defp get_withdrawal_stats_for_users(user_ids, today_start, today_end) do
    try do
      # 获取提现记录
      withdrawal_query = Teen.PaymentSystem.WithdrawalRecord
      |> Ash.Query.filter(expr(user_id in ^user_ids and audit_status == 1 and payout_status == 2))

      case Ash.read(withdrawal_query) do
        {:ok, records} ->
          # 总提现金额
          total_amount = records
          |> Enum.reduce(Decimal.new(0), fn record, acc ->
            Decimal.add(acc, record.amount || Decimal.new(0))
          end)

          # 提现用户数
          total_users = records
          |> Enum.map(& &1.user_id)
          |> Enum.uniq()
          |> length()

          %{
            total_amount: total_amount,
            total_users: total_users
          }

        {:error, _} ->
          %{
            total_amount: Decimal.new(0),
            total_users: 0
          }
      end
    rescue
      _ ->
        %{
          total_amount: Decimal.new(0),
          total_users: 0
        }
    end
  end
end
