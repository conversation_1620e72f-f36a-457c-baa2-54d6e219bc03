defmodule Teen.Statistics.SystemSummaryReport do
  @moduledoc """
  系统总报表资源

  汇总所有渠道的数据，生成系统级别的报表
  包括日报表、月报表等不同时间维度的统计
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: Ash.DataLayer.Ets,
    domain: Teen.Statistics,
    extensions: [AshAdmin.Resource]

  import Ash.Expr

  admin do
    table_columns [
      :report_date,
      :report_type,
      :total_channels,
      :new_registrations,
      :active_users,
      :total_recharge_amount,
      :new_recharge_amount,
      :paying_users,
      :payment_rate,
      :arpu,
      :arppu
    ]
  end

  code_interface do
    define :generate_system_daily_report, args: [:report_date]
    define :generate_system_monthly_report, args: [:year, :month]
    define :get_system_report, args: [:report_date, :report_type]
    define :get_online_statistics
    define :get_realtime_data
  end

  actions do
    defaults [:read]

    action :generate_system_daily_report, :struct do
      argument :report_date, :date, default: &Date.utc_today/0

      run fn input, _context ->
        report_date = input.arguments.report_date

        # 获取所有渠道的报表数据
        case Teen.Statistics.ChannelReport.get_all_channel_reports(%{report_date: report_date}) do
          {:ok, channel_reports} ->
            system_report = aggregate_channel_reports(channel_reports, report_date, :daily)
            {:ok, system_report}

          {:error, reason} ->
            {:error, reason}
        end
      end
    end

    action :generate_system_monthly_report, :struct do
      argument :year, :integer, allow_nil?: false
      argument :month, :integer, allow_nil?: false

      run fn input, _context ->
        year = input.arguments.year
        month = input.arguments.month

        # 获取该月所有日期的报表数据并汇总
        start_date = Date.new!(year, month, 1)
        end_date = Date.end_of_month(start_date)

        monthly_report = generate_monthly_summary(start_date, end_date)
        {:ok, monthly_report}
      end
    end

    action :get_system_report, :struct do
      argument :report_date, :date, allow_nil?: false
      argument :report_type, :atom, allow_nil?: false

      run fn input, _context ->
        report_date = input.arguments.report_date
        report_type = input.arguments.report_type

        case report_type do
          :daily ->
            Teen.Statistics.SystemSummaryReport.generate_system_daily_report(%{report_date: report_date})

          :monthly ->
            Teen.Statistics.SystemSummaryReport.generate_system_monthly_report(%{
              year: report_date.year,
              month: report_date.month
            })

          _ ->
            {:error, "不支持的报表类型"}
        end
      end
    end

    action :get_online_statistics, :struct do
      run fn _input, _context ->
        online_stats = calculate_online_statistics()
        {:ok, online_stats}
      end
    end

    action :get_realtime_data, :struct do
      run fn _input, _context ->
        realtime_data = calculate_realtime_data()
        {:ok, realtime_data}
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :report_date, :date do
      allow_nil? false
      public? true
      description "报表日期"
    end

    attribute :report_type, :atom do
      allow_nil? false
      public? true
      description "报表类型"
      constraints one_of: [:daily, :monthly, :online, :realtime]
      default :daily
    end

    # 渠道统计
    attribute :total_channels, :integer do
      allow_nil? false
      public? true
      description "总渠道数"
      default 0
    end

    attribute :active_channels, :integer do
      allow_nil? false
      public? true
      description "活跃渠道数"
      default 0
    end

    # 用户统计（汇总所有渠道）
    attribute :new_devices, :integer do
      allow_nil? false
      public? true
      description "新增设备数"
      default 0
    end

    attribute :new_registrations, :integer do
      allow_nil? false
      public? true
      description "新增注册数"
      default 0
    end

    attribute :valid_new_users, :integer do
      allow_nil? false
      public? true
      description "有效新增用户数"
      default 0
    end

    attribute :mobile_bound_registrations, :integer do
      allow_nil? false
      public? true
      description "手机注册绑定数"
      default 0
    end

    attribute :active_users, :integer do
      allow_nil? false
      public? true
      description "活跃用户数"
      default 0
    end

    attribute :valid_active_users, :integer do
      allow_nil? false
      public? true
      description "有效活跃用户数"
      default 0
    end

    attribute :active_mobile_bound, :integer do
      allow_nil? false
      public? true
      description "活跃手机绑定用户数"
      default 0
    end

    attribute :online_users, :integer do
      allow_nil? false
      public? true
      description "当前在线用户数"
      default 0
    end

    # 充值统计（汇总所有渠道）
    attribute :paying_users, :integer do
      allow_nil? false
      public? true
      description "充值人数"
      default 0
    end

    attribute :new_paying_users, :integer do
      allow_nil? false
      public? true
      description "新增充值人数"
      default 0
    end

    attribute :first_time_payers, :integer do
      allow_nil? false
      public? true
      description "首付人数"
      default 0
    end

    attribute :total_recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "总充值额"
      default 0
    end

    attribute :new_recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "新增充值额"
      default 0
    end

    # 提现统计
    attribute :withdrawal_amount, :decimal do
      allow_nil? false
      public? true
      description "退出总额"
      default 0
    end

    attribute :withdrawal_users, :integer do
      allow_nil? false
      public? true
      description "退出人数"
      default 0
    end

    # 比率指标（系统平均值）
    attribute :withdrawal_recharge_ratio, :decimal do
      allow_nil? true
      public? true
      description "退充比（%）"
    end

    attribute :payment_rate, :decimal do
      allow_nil? true
      public? true
      description "付费率（%）"
    end

    attribute :new_payment_rate, :decimal do
      allow_nil? true
      public? true
      description "新增付费率（%）"
    end

    attribute :valid_payment_rate, :decimal do
      allow_nil? true
      public? true
      description "有效付费率（%）"
    end

    attribute :valid_new_payment_rate, :decimal do
      allow_nil? true
      public? true
      description "有效新增付费率（%）"
    end

    # ARPU指标（系统平均值）
    attribute :arpu, :decimal do
      allow_nil? true
      public? true
      description "ARPU（每用户平均收入）"
    end

    attribute :arppu, :decimal do
      allow_nil? true
      public? true
      description "ARPPU（每付费用户平均收入）"
    end

    attribute :valid_arpu, :decimal do
      allow_nil? true
      public? true
      description "有效ARPU"
    end

    # 留存率指标（系统平均值）
    attribute :next_day_retention, :decimal do
      allow_nil? true
      public? true
      description "次日留存率（%）"
    end

    attribute :day3_retention, :decimal do
      allow_nil? true
      public? true
      description "3日留存率（%）"
    end

    attribute :day7_retention, :decimal do
      allow_nil? true
      public? true
      description "7日留存率（%）"
    end

    attribute :day14_retention, :decimal do
      allow_nil? true
      public? true
      description "14日留存率（%）"
    end

    attribute :day30_retention, :decimal do
      allow_nil? true
      public? true
      description "30日留存率（%）"
    end

    # 游戏统计
    attribute :total_games_played, :integer do
      allow_nil? false
      public? true
      description "总游戏局数"
      default 0
    end

    attribute :games_played_today, :integer do
      allow_nil? false
      public? true
      description "今日游戏局数"
      default 0
    end

    attribute :avg_session_time, :decimal do
      allow_nil? true
      public? true
      description "平均会话时长（分钟）"
    end

    # 报表元数据
    attribute :report_data, :map do
      allow_nil? true
      public? true
      description "详细报表数据（JSON）"
      default %{}
    end

    timestamps()
  end

  # ==================== 私有辅助函数 ====================

  # 汇总渠道报表数据
  defp aggregate_channel_reports(channel_reports, report_date, report_type) do
    try do
      total_channels = length(channel_reports)
      active_channels = Enum.count(channel_reports, fn report -> report.active_users > 0 end)

      # 汇总各项指标
      aggregated = Enum.reduce(channel_reports, %{
        new_devices: 0,
        new_registrations: 0,
        valid_new_users: 0,
        mobile_bound_registrations: 0,
        active_users: 0,
        valid_active_users: 0,
        active_mobile_bound: 0,
        online_users: 0,
        paying_users: 0,
        new_paying_users: 0,
        first_time_payers: 0,
        total_recharge_amount: Decimal.new(0),
        new_recharge_amount: Decimal.new(0),
        withdrawal_amount: Decimal.new(0),
        withdrawal_users: 0,
        total_games_played: 0,
        games_played_today: 0
      }, fn report, acc ->
        %{
          new_devices: acc.new_devices + (report.new_devices || 0),
          new_registrations: acc.new_registrations + (report.new_registrations || 0),
          valid_new_users: acc.valid_new_users + (report.valid_new_users || 0),
          mobile_bound_registrations: acc.mobile_bound_registrations + (report.mobile_bound_registrations || 0),
          active_users: acc.active_users + (report.active_users || 0),
          valid_active_users: acc.valid_active_users + (report.valid_active_users || 0),
          active_mobile_bound: acc.active_mobile_bound + (report.active_mobile_bound || 0),
          online_users: acc.online_users + (report.online_users || 0),
          paying_users: acc.paying_users + (report.paying_users || 0),
          new_paying_users: acc.new_paying_users + (report.new_paying_users || 0),
          first_time_payers: acc.first_time_payers + (report.first_time_payers || 0),
          total_recharge_amount: Decimal.add(acc.total_recharge_amount, report.total_recharge_amount || Decimal.new(0)),
          new_recharge_amount: Decimal.add(acc.new_recharge_amount, report.new_recharge_amount || Decimal.new(0)),
          withdrawal_amount: Decimal.add(acc.withdrawal_amount, report.withdrawal_amount || Decimal.new(0)),
          withdrawal_users: acc.withdrawal_users + (report.withdrawal_users || 0),
          total_games_played: acc.total_games_played + (report.total_games_played || 0),
          games_played_today: acc.games_played_today + (report.games_played_today || 0)
        }
      end)

      # 计算系统级别的比率指标
      system_rates = calculate_system_rates(aggregated)
      system_retention = calculate_system_retention(channel_reports)
      avg_session_time = calculate_system_avg_session_time(channel_reports)

      %{
        id: Ash.UUID.generate(),
        report_date: report_date,
        report_type: report_type,
        total_channels: total_channels,
        active_channels: active_channels,

        # 用户指标
        new_devices: aggregated.new_devices,
        new_registrations: aggregated.new_registrations,
        valid_new_users: aggregated.valid_new_users,
        mobile_bound_registrations: aggregated.mobile_bound_registrations,
        active_users: aggregated.active_users,
        valid_active_users: aggregated.valid_active_users,
        active_mobile_bound: aggregated.active_mobile_bound,
        online_users: aggregated.online_users,

        # 充值指标
        paying_users: aggregated.paying_users,
        new_paying_users: aggregated.new_paying_users,
        first_time_payers: aggregated.first_time_payers,
        total_recharge_amount: aggregated.total_recharge_amount,
        new_recharge_amount: aggregated.new_recharge_amount,

        # 提现指标
        withdrawal_amount: aggregated.withdrawal_amount,
        withdrawal_users: aggregated.withdrawal_users,

        # 比率指标
        withdrawal_recharge_ratio: system_rates.withdrawal_recharge_ratio,
        payment_rate: system_rates.payment_rate,
        new_payment_rate: system_rates.new_payment_rate,
        valid_payment_rate: system_rates.valid_payment_rate,
        valid_new_payment_rate: system_rates.valid_new_payment_rate,

        # ARPU指标
        arpu: system_rates.arpu,
        arppu: system_rates.arppu,
        valid_arpu: system_rates.valid_arpu,

        # 留存率指标
        next_day_retention: system_retention.next_day_retention,
        day3_retention: system_retention.day3_retention,
        day7_retention: system_retention.day7_retention,
        day14_retention: system_retention.day14_retention,
        day30_retention: system_retention.day30_retention,

        # 游戏指标
        total_games_played: aggregated.total_games_played,
        games_played_today: aggregated.games_played_today,
        avg_session_time: avg_session_time,

        # 报表数据
        report_data: %{
          channel_count: total_channels,
          active_channel_count: active_channels,
          aggregated_data: aggregated
        },

        inserted_at: DateTime.utc_now(),
        updated_at: DateTime.utc_now()
      }
    rescue
      error ->
        %{
          id: Ash.UUID.generate(),
          report_date: report_date,
          report_type: report_type,
          total_channels: 0,
          active_channels: 0,
          new_devices: 0,
          new_registrations: 0,
          valid_new_users: 0,
          mobile_bound_registrations: 0,
          active_users: 0,
          valid_active_users: 0,
          active_mobile_bound: 0,
          online_users: 0,
          paying_users: 0,
          new_paying_users: 0,
          first_time_payers: 0,
          total_recharge_amount: Decimal.new(0),
          new_recharge_amount: Decimal.new(0),
          withdrawal_amount: Decimal.new(0),
          withdrawal_users: 0,
          withdrawal_recharge_ratio: Decimal.new(0),
          payment_rate: Decimal.new(0),
          new_payment_rate: Decimal.new(0),
          valid_payment_rate: Decimal.new(0),
          valid_new_payment_rate: Decimal.new(0),
          arpu: Decimal.new(0),
          arppu: Decimal.new(0),
          valid_arpu: Decimal.new(0),
          next_day_retention: Decimal.new(0),
          day3_retention: Decimal.new(0),
          day7_retention: Decimal.new(0),
          day14_retention: Decimal.new(0),
          day30_retention: Decimal.new(0),
          total_games_played: 0,
          games_played_today: 0,
          avg_session_time: Decimal.new(0),
          report_data: %{error: inspect(error)},
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        }
    end
  end

  # 计算系统级别的比率指标
  defp calculate_system_rates(aggregated) do
    try do
      # 付费率 = 充值人数 / 活跃用户数
      payment_rate = if aggregated.active_users > 0 do
        Decimal.div(Decimal.new(aggregated.paying_users), Decimal.new(aggregated.active_users))
        |> Decimal.mult(Decimal.new(100))
      else
        Decimal.new(0)
      end

      # 新增付费率 = 新增充值人数 / 新增注册数
      new_payment_rate = if aggregated.new_registrations > 0 do
        Decimal.div(Decimal.new(aggregated.new_paying_users), Decimal.new(aggregated.new_registrations))
        |> Decimal.mult(Decimal.new(100))
      else
        Decimal.new(0)
      end

      # 有效付费率 = 充值人数 / 有效活跃用户数
      valid_payment_rate = if aggregated.valid_active_users > 0 do
        Decimal.div(Decimal.new(aggregated.paying_users), Decimal.new(aggregated.valid_active_users))
        |> Decimal.mult(Decimal.new(100))
      else
        Decimal.new(0)
      end

      # 有效新增付费率 = 新增充值人数 / 有效新增用户数
      valid_new_payment_rate = if aggregated.valid_new_users > 0 do
        Decimal.div(Decimal.new(aggregated.new_paying_users), Decimal.new(aggregated.valid_new_users))
        |> Decimal.mult(Decimal.new(100))
      else
        Decimal.new(0)
      end

      # 退充比 = 提现总额 / 充值总额
      withdrawal_recharge_ratio = if Decimal.compare(aggregated.total_recharge_amount, Decimal.new(0)) == :gt do
        Decimal.div(aggregated.withdrawal_amount, aggregated.total_recharge_amount)
        |> Decimal.mult(Decimal.new(100))
      else
        Decimal.new(0)
      end

      # ARPU = 总收入 / 活跃用户数
      arpu = if aggregated.active_users > 0 do
        Decimal.div(aggregated.total_recharge_amount, Decimal.new(aggregated.active_users))
      else
        Decimal.new(0)
      end

      # ARPPU = 总收入 / 付费用户数
      arppu = if aggregated.paying_users > 0 do
        Decimal.div(aggregated.total_recharge_amount, Decimal.new(aggregated.paying_users))
      else
        Decimal.new(0)
      end

      # 有效ARPU = 总收入 / 有效活跃用户数
      valid_arpu = if aggregated.valid_active_users > 0 do
        Decimal.div(aggregated.total_recharge_amount, Decimal.new(aggregated.valid_active_users))
      else
        Decimal.new(0)
      end

      %{
        payment_rate: payment_rate,
        new_payment_rate: new_payment_rate,
        valid_payment_rate: valid_payment_rate,
        valid_new_payment_rate: valid_new_payment_rate,
        withdrawal_recharge_ratio: withdrawal_recharge_ratio,
        arpu: arpu,
        arppu: arppu,
        valid_arpu: valid_arpu
      }
    rescue
      _ ->
        %{
          payment_rate: Decimal.new(0),
          new_payment_rate: Decimal.new(0),
          valid_payment_rate: Decimal.new(0),
          valid_new_payment_rate: Decimal.new(0),
          withdrawal_recharge_ratio: Decimal.new(0),
          arpu: Decimal.new(0),
          arppu: Decimal.new(0),
          valid_arpu: Decimal.new(0)
        }
    end
  end

  # 计算系统级别的留存率（各渠道平均值）
  defp calculate_system_retention(channel_reports) do
    try do
      if Enum.empty?(channel_reports) do
        %{
          next_day_retention: Decimal.new(0),
          day3_retention: Decimal.new(0),
          day7_retention: Decimal.new(0),
          day14_retention: Decimal.new(0),
          day30_retention: Decimal.new(0)
        }
      else
        count = length(channel_reports)

        # 计算各留存率的平均值
        totals = Enum.reduce(channel_reports, %{
          next_day: Decimal.new(0),
          day3: Decimal.new(0),
          day7: Decimal.new(0),
          day14: Decimal.new(0),
          day30: Decimal.new(0)
        }, fn report, acc ->
          %{
            next_day: Decimal.add(acc.next_day, report.next_day_retention || Decimal.new(0)),
            day3: Decimal.add(acc.day3, report.day3_retention || Decimal.new(0)),
            day7: Decimal.add(acc.day7, report.day7_retention || Decimal.new(0)),
            day14: Decimal.add(acc.day14, report.day14_retention || Decimal.new(0)),
            day30: Decimal.add(acc.day30, report.day30_retention || Decimal.new(0))
          }
        end)

        %{
          next_day_retention: Decimal.div(totals.next_day, Decimal.new(count)),
          day3_retention: Decimal.div(totals.day3, Decimal.new(count)),
          day7_retention: Decimal.div(totals.day7, Decimal.new(count)),
          day14_retention: Decimal.div(totals.day14, Decimal.new(count)),
          day30_retention: Decimal.div(totals.day30, Decimal.new(count))
        }
      end
    rescue
      _ ->
        %{
          next_day_retention: Decimal.new(0),
          day3_retention: Decimal.new(0),
          day7_retention: Decimal.new(0),
          day14_retention: Decimal.new(0),
          day30_retention: Decimal.new(0)
        }
    end
  end

  # 计算系统级别的平均会话时长
  defp calculate_system_avg_session_time(channel_reports) do
    try do
      if Enum.empty?(channel_reports) do
        Decimal.new(0)
      else
        valid_reports = Enum.filter(channel_reports, fn report ->
          report.avg_session_time != nil && Decimal.compare(report.avg_session_time, Decimal.new(0)) == :gt
        end)

        if Enum.empty?(valid_reports) do
          Decimal.new(0)
        else
          total_time = Enum.reduce(valid_reports, Decimal.new(0), fn report, acc ->
            Decimal.add(acc, report.avg_session_time)
          end)

          Decimal.div(total_time, Decimal.new(length(valid_reports)))
        end
      end
    rescue
      _ -> Decimal.new(0)
    end
  end

  # 生成月度汇总报表
  defp generate_monthly_summary(start_date, end_date) do
    try do
      # 这里应该获取该月每日的报表数据并汇总
      # 简化处理，返回基础结构
      %{
        id: Ash.UUID.generate(),
        report_date: start_date,
        report_type: :monthly,
        total_channels: 0,
        active_channels: 0,
        new_devices: 0,
        new_registrations: 0,
        valid_new_users: 0,
        mobile_bound_registrations: 0,
        active_users: 0,
        valid_active_users: 0,
        active_mobile_bound: 0,
        online_users: 0,
        paying_users: 0,
        new_paying_users: 0,
        first_time_payers: 0,
        total_recharge_amount: Decimal.new(0),
        new_recharge_amount: Decimal.new(0),
        withdrawal_amount: Decimal.new(0),
        withdrawal_users: 0,
        withdrawal_recharge_ratio: Decimal.new(0),
        payment_rate: Decimal.new(0),
        new_payment_rate: Decimal.new(0),
        valid_payment_rate: Decimal.new(0),
        valid_new_payment_rate: Decimal.new(0),
        arpu: Decimal.new(0),
        arppu: Decimal.new(0),
        valid_arpu: Decimal.new(0),
        next_day_retention: Decimal.new(0),
        day3_retention: Decimal.new(0),
        day7_retention: Decimal.new(0),
        day14_retention: Decimal.new(0),
        day30_retention: Decimal.new(0),
        total_games_played: 0,
        games_played_today: 0,
        avg_session_time: Decimal.new(0),
        report_data: %{
          period: "#{start_date} - #{end_date}",
          type: "monthly_summary"
        },
        inserted_at: DateTime.utc_now(),
        updated_at: DateTime.utc_now()
      }
    rescue
      _ ->
        %{
          id: Ash.UUID.generate(),
          report_date: start_date,
          report_type: :monthly,
          total_channels: 0,
          active_channels: 0,
          report_data: %{error: "生成月度报表失败"},
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        }
    end
  end

  # 计算在线统计
  defp calculate_online_statistics do
    try do
      # 获取当前在线用户统计
      online_users = get_total_online_users()
      active_rooms = get_active_game_rooms()

      %{
        id: Ash.UUID.generate(),
        report_date: Date.utc_today(),
        report_type: :online,
        online_users: online_users,
        active_rooms: active_rooms,
        report_data: %{
          timestamp: DateTime.utc_now(),
          type: "online_statistics"
        },
        inserted_at: DateTime.utc_now(),
        updated_at: DateTime.utc_now()
      }
    rescue
      _ ->
        %{
          id: Ash.UUID.generate(),
          report_date: Date.utc_today(),
          report_type: :online,
          online_users: 0,
          report_data: %{error: "获取在线统计失败"},
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        }
    end
  end

  # 计算实时数据
  defp calculate_realtime_data do
    try do
      # 获取实时数据
      current_time = DateTime.utc_now()
      today = Date.utc_today()

      # 获取详细的游戏统计
      game_details = get_today_game_details()

      %{
        id: Ash.UUID.generate(),
        report_date: today,
        report_type: :realtime,
        online_users: get_total_online_users(),
        new_registrations: get_today_registrations(),
        new_recharge_amount: get_today_recharge_amount(),
        games_played_today: game_details.total_games,
        total_games_played: get_total_games_count(),
        active_users: game_details.active_game_users,
        report_data: %{
          timestamp: current_time,
          type: "realtime_data",
          last_update: current_time,
          game_details: game_details,
          active_game_users: game_details.active_game_users,
          games_by_type: game_details.games_by_type,
          game_win_rate: game_details.win_rate,
          total_bet_amount: game_details.total_bet_amount,
          total_win_amount: game_details.total_win_amount
        },
        inserted_at: current_time,
        updated_at: current_time
      }
    rescue
      _ ->
        %{
          id: Ash.UUID.generate(),
          report_date: Date.utc_today(),
          report_type: :realtime,
          online_users: 0,
          games_played_today: 0,
          total_games_played: 0,
          report_data: %{error: "获取实时数据失败"},
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        }
    end
  end

  # 获取总在线用户数
  defp get_total_online_users do
    try do
      case Cypridina.Accounts.User.read() do
        {:ok, users} ->
          users
          |> Enum.count(fn user -> is_nil(user.last_offline_at) end)

        {:error, _} -> 0
      end
    rescue
      _ -> 0
    end
  end

  # 获取活跃游戏房间数
  defp get_active_game_rooms do
    try do
      # 这里应该查询活跃的游戏房间
      # 简化处理
      10
    rescue
      _ -> 0
    end
  end

  # 获取今日注册数
  defp get_today_registrations do
    try do
      today_start = DateTime.new!(Date.utc_today(), ~T[00:00:00])
      today_end = DateTime.new!(Date.utc_today(), ~T[23:59:59])

      case Cypridina.Accounts.User.read() do
        {:ok, users} ->
          users
          |> Enum.count(fn user ->
            user.inserted_at >= today_start && user.inserted_at <= today_end
          end)

        {:error, _} -> 0
      end
    rescue
      _ -> 0
    end
  end

  # 获取今日充值金额
  defp get_today_recharge_amount do
    try do
      today_start = DateTime.new!(Date.utc_today(), ~T[00:00:00])
      today_end = DateTime.new!(Date.utc_today(), ~T[23:59:59])

      recharge_query = Teen.PaymentSystem.RechargeRecord
      |> Ash.Query.filter(expr(status == 1 and inserted_at >= ^today_start and inserted_at <= ^today_end))

      case Ash.read(recharge_query) do
        {:ok, records} ->
          records
          |> Enum.reduce(Decimal.new(0), fn record, acc ->
            Decimal.add(acc, record.amount || Decimal.new(0))
          end)

        {:error, _} -> Decimal.new(0)
      end
    rescue
      _ -> Decimal.new(0)
    end
  end

  # 获取今日游戏局数
  defp get_today_games_count do
    try do
      today_start = DateTime.new!(Date.utc_today(), ~T[00:00:00])
      today_end = DateTime.new!(Date.utc_today(), ~T[23:59:59])

      # 查询今日的游戏记录，排除取消的游戏
      today_games_query = Teen.GameManagement.GameRecord
      |> Ash.Query.filter(
        expr(inserted_at >= ^today_start and
        inserted_at <= ^today_end and
        result_status != :cancelled)
      )

      case Ash.read(today_games_query) do
        {:ok, game_records} ->
          length(game_records)

        {:error, _} ->
          0
      end
    rescue
      _ -> 0
    end
  end

  # 获取总游戏局数
  defp get_total_games_count do
    try do
      # 查询所有游戏记录，排除取消的游戏
      total_games_query = Teen.GameManagement.GameRecord
      |> Ash.Query.filter(expr(result_status != :cancelled))

      case Ash.read(total_games_query) do
        {:ok, game_records} ->
          length(game_records)

        {:error, _} ->
          0
      end
    rescue
      _ -> 0
    end
  end

  # 获取今日活跃游戏用户数
  defp get_today_active_game_users do
    try do
      today_start = DateTime.new!(Date.utc_today(), ~T[00:00:00])
      today_end = DateTime.new!(Date.utc_today(), ~T[23:59:59])

      # 查询今日有游戏记录的用户
      today_games_query = Teen.GameManagement.GameRecord
      |> Ash.Query.filter(
        expr(inserted_at >= ^today_start and
        inserted_at <= ^today_end and
        result_status != :cancelled)
      )

      case Ash.read(today_games_query) do
        {:ok, game_records} ->
          game_records
          |> Enum.map(& &1.user_id)
          |> Enum.uniq()
          |> length()

        {:error, _} ->
          0
      end
    rescue
      _ -> 0
    end
  end

  # 获取今日游戏详细统计
  defp get_today_game_details do
    try do
      today_start = DateTime.new!(Date.utc_today(), ~T[00:00:00])
      today_end = DateTime.new!(Date.utc_today(), ~T[23:59:59])

      # 查询今日的游戏记录
      today_games_query = Teen.GameManagement.GameRecord
      |> Ash.Query.filter(
        expr(inserted_at >= ^today_start and
        inserted_at <= ^today_end and
        result_status != :cancelled)
      )

      case Ash.read(today_games_query) do
        {:ok, game_records} ->
          total_games = length(game_records)

          # 按游戏类型分组统计
          games_by_type = game_records
          |> Enum.group_by(& &1.game_type)
          |> Enum.map(fn {type, records} -> {type, length(records)} end)
          |> Enum.into(%{})

          # 胜负统计
          win_games = Enum.count(game_records, & &1.result_status == :win)
          lose_games = Enum.count(game_records, & &1.result_status == :lose)
          draw_games = Enum.count(game_records, & &1.result_status == :draw)
          in_progress_games = Enum.count(game_records, & &1.result_status == :in_progress)

          # 活跃游戏用户数
          active_game_users = game_records
          |> Enum.map(& &1.user_id)
          |> Enum.uniq()
          |> length()

          # 总下注金额
          total_bet_amount = game_records
          |> Enum.reduce(Decimal.new(0), fn record, acc ->
            Decimal.add(acc, record.bet_amount || Decimal.new(0))
          end)

          # 总赢得金额
          total_win_amount = game_records
          |> Enum.reduce(Decimal.new(0), fn record, acc ->
            Decimal.add(acc, record.win_amount || Decimal.new(0))
          end)

          %{
            total_games: total_games,
            games_by_type: games_by_type,
            win_games: win_games,
            lose_games: lose_games,
            draw_games: draw_games,
            in_progress_games: in_progress_games,
            active_game_users: active_game_users,
            total_bet_amount: total_bet_amount,
            total_win_amount: total_win_amount,
            win_rate: (if total_games > 0, do: Float.round(win_games / total_games * 100, 2), else: 0.0)
          }

        {:error, _} ->
          %{
            total_games: 0,
            games_by_type: %{},
            win_games: 0,
            lose_games: 0,
            draw_games: 0,
            in_progress_games: 0,
            active_game_users: 0,
            total_bet_amount: Decimal.new(0),
            total_win_amount: Decimal.new(0),
            win_rate: 0.0
          }
      end
    rescue
      _ ->
        %{
          total_games: 0,
          games_by_type: %{},
          win_games: 0,
          lose_games: 0,
          draw_games: 0,
          in_progress_games: 0,
          active_game_users: 0,
          total_bet_amount: Decimal.new(0),
          total_win_amount: Decimal.new(0),
          win_rate: 0.0
        }
    end
  end
end
