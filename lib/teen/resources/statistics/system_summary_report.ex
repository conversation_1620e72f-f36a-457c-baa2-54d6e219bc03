defmodule Teen.Statistics.SystemSummaryReport do
  @moduledoc """
  系统总报表资源

  汇总所有渠道的数据，生成系统级别的报表
  包括日报表、月报表等不同时间维度的统计
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: Ash.DataLayer.Ets,
    domain: Teen.Statistics,
    extensions: [AshAdmin.Resource]

  import Ash.Expr

  admin do
    table_columns [
      :report_date,
      :report_type,
      :total_channels,
      :new_registrations,
      :active_users,
      :total_recharge_amount,
      :new_recharge_amount,
      :paying_users,
      :payment_rate,
      :arpu,
      :arppu
    ]
  end

  code_interface do
    define :generate_system_daily_report, args: [:report_date]
    define :generate_system_monthly_report, args: [:year, :month]
    define :get_system_report, args: [:report_date, :report_type]
    define :get_online_statistics
    define :get_realtime_data
    define :get_comprehensive_metrics, args: [:report_date]
    define :export_report_data, args: [:start_date, :end_date, :format]
    define :get_chart_data, args: [:chart_type, :start_date, :end_date, :metrics]
    define :get_retention_analysis, args: [:cohort_date]
  end

  actions do
    defaults [:read]

    action :generate_system_daily_report, :struct do
      argument :report_date, :date, default: &Date.utc_today/0

      run fn input, _context ->
        report_date = input.arguments.report_date

        try do
          # 生成综合指标数据
          comprehensive_data = calculate_comprehensive_metrics(report_date)
          {:ok, comprehensive_data}
        rescue
          error ->
            Logger.error("生成系统日报失败: #{inspect(error)}")
            {:error, "生成系统日报失败: #{inspect(error)}"}
        end
      end
    end

    action :generate_system_monthly_report, :struct do
      argument :year, :integer, allow_nil?: false
      argument :month, :integer, allow_nil?: false

      run fn input, _context ->
        year = input.arguments.year
        month = input.arguments.month

        # 获取该月所有日期的报表数据并汇总
        start_date = Date.new!(year, month, 1)
        end_date = Date.end_of_month(start_date)

        monthly_report = generate_monthly_summary(start_date, end_date)
        {:ok, monthly_report}
      end
    end

    action :get_system_report, :struct do
      argument :report_date, :date, allow_nil?: false
      argument :report_type, :atom, allow_nil?: false

      run fn input, _context ->
        report_date = input.arguments.report_date
        report_type = input.arguments.report_type

        case report_type do
          :daily ->
            Teen.Statistics.SystemSummaryReport.generate_system_daily_report(%{report_date: report_date})

          :monthly ->
            Teen.Statistics.SystemSummaryReport.generate_system_monthly_report(%{
              year: report_date.year,
              month: report_date.month
            })

          _ ->
            {:error, "不支持的报表类型"}
        end
      end
    end

    action :get_online_statistics, :struct do
      run fn _input, _context ->
        online_stats = calculate_online_statistics()
        {:ok, online_stats}
      end
    end

    action :get_realtime_data, :struct do
      run fn _input, _context ->
        realtime_data = calculate_realtime_data()
        {:ok, realtime_data}
      end
    end

    # 获取综合指标数据
    action :get_comprehensive_metrics, :struct do
      argument :report_date, :date, default: &Date.utc_today/0

      run fn input, _context ->
        report_date = input.arguments.report_date
        comprehensive_data = calculate_comprehensive_metrics(report_date)
        {:ok, comprehensive_data}
      end
    end

    # 导出报表数据
    action :export_report_data, :struct do
      argument :start_date, :date, allow_nil?: false
      argument :end_date, :date, allow_nil?: false
      argument :format, :atom, default: :csv

      run fn input, _context ->
        start_date = input.arguments.start_date
        end_date = input.arguments.end_date
        format = input.arguments.format

        export_data = generate_export_data(start_date, end_date, format)
        {:ok, export_data}
      end
    end

    # 获取图表数据
    action :get_chart_data, :struct do
      argument :chart_type, :atom, allow_nil?: false
      argument :start_date, :date, allow_nil?: false
      argument :end_date, :date, allow_nil?: false
      argument :metrics, {:array, :atom}, default: []

      run fn input, _context ->
        chart_type = input.arguments.chart_type
        start_date = input.arguments.start_date
        end_date = input.arguments.end_date
        metrics = input.arguments.metrics

        chart_data = generate_chart_data(chart_type, start_date, end_date, metrics)
        {:ok, chart_data}
      end
    end

    # 获取留存分析数据
    action :get_retention_analysis, :struct do
      argument :cohort_date, :date, allow_nil?: false

      run fn input, _context ->
        cohort_date = input.arguments.cohort_date
        retention_data = calculate_retention_analysis(cohort_date)
        {:ok, retention_data}
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :report_date, :date do
      allow_nil? false
      public? true
      description "报表日期"
    end

    attribute :report_type, :atom do
      allow_nil? false
      public? true
      description "报表类型"
      constraints one_of: [:daily, :monthly, :online, :realtime]
      default :daily
    end

    # 渠道统计
    attribute :total_channels, :integer do
      allow_nil? false
      public? true
      description "总渠道数"
      default 0
    end

    attribute :active_channels, :integer do
      allow_nil? false
      public? true
      description "活跃渠道数"
      default 0
    end

    # 用户统计（汇总所有渠道）
    attribute :new_devices, :integer do
      allow_nil? false
      public? true
      description "新增设备数"
      default 0
    end

    attribute :new_registrations, :integer do
      allow_nil? false
      public? true
      description "新增注册数"
      default 0
    end

    attribute :valid_new_users, :integer do
      allow_nil? false
      public? true
      description "有效新增用户数"
      default 0
    end

    attribute :mobile_bound_registrations, :integer do
      allow_nil? false
      public? true
      description "手机注册绑定数"
      default 0
    end

    attribute :active_users, :integer do
      allow_nil? false
      public? true
      description "活跃用户数"
      default 0
    end

    attribute :valid_active_users, :integer do
      allow_nil? false
      public? true
      description "有效活跃用户数"
      default 0
    end

    attribute :active_mobile_bound, :integer do
      allow_nil? false
      public? true
      description "活跃手机绑定用户数"
      default 0
    end

    attribute :online_users, :integer do
      allow_nil? false
      public? true
      description "当前在线用户数"
      default 0
    end

    # 充值统计（汇总所有渠道）
    attribute :paying_users, :integer do
      allow_nil? false
      public? true
      description "充值人数"
      default 0
    end

    attribute :new_paying_users, :integer do
      allow_nil? false
      public? true
      description "新增充值人数"
      default 0
    end

    attribute :first_time_payers, :integer do
      allow_nil? false
      public? true
      description "首付人数"
      default 0
    end

    attribute :total_recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "总充值额"
      default 0
    end

    attribute :new_recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "新增充值额"
      default 0
    end

    # 提现统计
    attribute :withdrawal_amount, :decimal do
      allow_nil? false
      public? true
      description "退出总额"
      default 0
    end

    attribute :withdrawal_users, :integer do
      allow_nil? false
      public? true
      description "退出人数"
      default 0
    end

    # 比率指标（系统平均值）
    attribute :withdrawal_recharge_ratio, :decimal do
      allow_nil? true
      public? true
      description "退充比（%）"
    end

    attribute :payment_rate, :decimal do
      allow_nil? true
      public? true
      description "付费率（%）"
    end

    attribute :new_payment_rate, :decimal do
      allow_nil? true
      public? true
      description "新增付费率（%）"
    end

    attribute :valid_payment_rate, :decimal do
      allow_nil? true
      public? true
      description "有效付费率（%）"
    end

    attribute :valid_new_payment_rate, :decimal do
      allow_nil? true
      public? true
      description "有效新增付费率（%）"
    end

    # ARPU指标（系统平均值）
    attribute :arpu, :decimal do
      allow_nil? true
      public? true
      description "ARPU（每用户平均收入）"
    end

    attribute :arppu, :decimal do
      allow_nil? true
      public? true
      description "ARPPU（每付费用户平均收入）"
    end

    attribute :valid_arpu, :decimal do
      allow_nil? true
      public? true
      description "有效ARPU"
    end

    # 留存率指标（系统平均值）
    attribute :next_day_retention, :decimal do
      allow_nil? true
      public? true
      description "次日留存率（%）"
    end

    attribute :day3_retention, :decimal do
      allow_nil? true
      public? true
      description "3日留存率（%）"
    end

    attribute :day7_retention, :decimal do
      allow_nil? true
      public? true
      description "7日留存率（%）"
    end

    attribute :day14_retention, :decimal do
      allow_nil? true
      public? true
      description "14日留存率（%）"
    end

    attribute :day30_retention, :decimal do
      allow_nil? true
      public? true
      description "30日留存率（%）"
    end

    # 游戏统计
    attribute :total_games_played, :integer do
      allow_nil? false
      public? true
      description "总游戏局数"
      default 0
    end

    attribute :games_played_today, :integer do
      allow_nil? false
      public? true
      description "今日游戏局数"
      default 0
    end

    attribute :avg_session_time, :decimal do
      allow_nil? true
      public? true
      description "平均会话时长（分钟）"
    end

    # 报表元数据
    attribute :report_data, :map do
      allow_nil? true
      public? true
      description "详细报表数据（JSON）"
      default %{}
    end

    timestamps()
  end

  # ==================== 私有辅助函数 ====================

  # 汇总渠道报表数据
  defp aggregate_channel_reports(channel_reports, report_date, report_type) do
    try do
      total_channels = length(channel_reports)
      active_channels = Enum.count(channel_reports, fn report -> report.active_users > 0 end)

      # 汇总各项指标
      aggregated = Enum.reduce(channel_reports, %{
        new_devices: 0,
        new_registrations: 0,
        valid_new_users: 0,
        mobile_bound_registrations: 0,
        active_users: 0,
        valid_active_users: 0,
        active_mobile_bound: 0,
        online_users: 0,
        paying_users: 0,
        new_paying_users: 0,
        first_time_payers: 0,
        total_recharge_amount: Decimal.new(0),
        new_recharge_amount: Decimal.new(0),
        withdrawal_amount: Decimal.new(0),
        withdrawal_users: 0,
        total_games_played: 0,
        games_played_today: 0
      }, fn report, acc ->
        %{
          new_devices: acc.new_devices + (report.new_devices || 0),
          new_registrations: acc.new_registrations + (report.new_registrations || 0),
          valid_new_users: acc.valid_new_users + (report.valid_new_users || 0),
          mobile_bound_registrations: acc.mobile_bound_registrations + (report.mobile_bound_registrations || 0),
          active_users: acc.active_users + (report.active_users || 0),
          valid_active_users: acc.valid_active_users + (report.valid_active_users || 0),
          active_mobile_bound: acc.active_mobile_bound + (report.active_mobile_bound || 0),
          online_users: acc.online_users + (report.online_users || 0),
          paying_users: acc.paying_users + (report.paying_users || 0),
          new_paying_users: acc.new_paying_users + (report.new_paying_users || 0),
          first_time_payers: acc.first_time_payers + (report.first_time_payers || 0),
          total_recharge_amount: Decimal.add(acc.total_recharge_amount, report.total_recharge_amount || Decimal.new(0)),
          new_recharge_amount: Decimal.add(acc.new_recharge_amount, report.new_recharge_amount || Decimal.new(0)),
          withdrawal_amount: Decimal.add(acc.withdrawal_amount, report.withdrawal_amount || Decimal.new(0)),
          withdrawal_users: acc.withdrawal_users + (report.withdrawal_users || 0),
          total_games_played: acc.total_games_played + (report.total_games_played || 0),
          games_played_today: acc.games_played_today + (report.games_played_today || 0)
        }
      end)

      # 计算系统级别的比率指标
      system_rates = calculate_system_rates(aggregated)
      system_retention = calculate_system_retention(channel_reports)
      avg_session_time = calculate_system_avg_session_time(channel_reports)

      %{
        id: Ash.UUID.generate(),
        report_date: report_date,
        report_type: report_type,
        total_channels: total_channels,
        active_channels: active_channels,

        # 用户指标
        new_devices: aggregated.new_devices,
        new_registrations: aggregated.new_registrations,
        valid_new_users: aggregated.valid_new_users,
        mobile_bound_registrations: aggregated.mobile_bound_registrations,
        active_users: aggregated.active_users,
        valid_active_users: aggregated.valid_active_users,
        active_mobile_bound: aggregated.active_mobile_bound,
        online_users: aggregated.online_users,

        # 充值指标
        paying_users: aggregated.paying_users,
        new_paying_users: aggregated.new_paying_users,
        first_time_payers: aggregated.first_time_payers,
        total_recharge_amount: aggregated.total_recharge_amount,
        new_recharge_amount: aggregated.new_recharge_amount,

        # 提现指标
        withdrawal_amount: aggregated.withdrawal_amount,
        withdrawal_users: aggregated.withdrawal_users,

        # 比率指标
        withdrawal_recharge_ratio: system_rates.withdrawal_recharge_ratio,
        payment_rate: system_rates.payment_rate,
        new_payment_rate: system_rates.new_payment_rate,
        valid_payment_rate: system_rates.valid_payment_rate,
        valid_new_payment_rate: system_rates.valid_new_payment_rate,

        # ARPU指标
        arpu: system_rates.arpu,
        arppu: system_rates.arppu,
        valid_arpu: system_rates.valid_arpu,

        # 留存率指标
        next_day_retention: system_retention.next_day_retention,
        day3_retention: system_retention.day3_retention,
        day7_retention: system_retention.day7_retention,
        day14_retention: system_retention.day14_retention,
        day30_retention: system_retention.day30_retention,

        # 游戏指标
        total_games_played: aggregated.total_games_played,
        games_played_today: aggregated.games_played_today,
        avg_session_time: avg_session_time,

        # 报表数据
        report_data: %{
          channel_count: total_channels,
          active_channel_count: active_channels,
          aggregated_data: aggregated
        },

        inserted_at: DateTime.utc_now(),
        updated_at: DateTime.utc_now()
      }
    rescue
      error ->
        %{
          id: Ash.UUID.generate(),
          report_date: report_date,
          report_type: report_type,
          total_channels: 0,
          active_channels: 0,
          new_devices: 0,
          new_registrations: 0,
          valid_new_users: 0,
          mobile_bound_registrations: 0,
          active_users: 0,
          valid_active_users: 0,
          active_mobile_bound: 0,
          online_users: 0,
          paying_users: 0,
          new_paying_users: 0,
          first_time_payers: 0,
          total_recharge_amount: Decimal.new(0),
          new_recharge_amount: Decimal.new(0),
          withdrawal_amount: Decimal.new(0),
          withdrawal_users: 0,
          withdrawal_recharge_ratio: Decimal.new(0),
          payment_rate: Decimal.new(0),
          new_payment_rate: Decimal.new(0),
          valid_payment_rate: Decimal.new(0),
          valid_new_payment_rate: Decimal.new(0),
          arpu: Decimal.new(0),
          arppu: Decimal.new(0),
          valid_arpu: Decimal.new(0),
          next_day_retention: Decimal.new(0),
          day3_retention: Decimal.new(0),
          day7_retention: Decimal.new(0),
          day14_retention: Decimal.new(0),
          day30_retention: Decimal.new(0),
          total_games_played: 0,
          games_played_today: 0,
          avg_session_time: Decimal.new(0),
          report_data: %{error: inspect(error)},
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        }
    end
  end

  # 计算系统级别的比率指标
  defp calculate_system_rates(aggregated) do
    try do
      # 付费率 = 充值人数 / 活跃用户数
      payment_rate = if aggregated.active_users > 0 do
        Decimal.div(Decimal.new(aggregated.paying_users), Decimal.new(aggregated.active_users))
        |> Decimal.mult(Decimal.new(100))
      else
        Decimal.new(0)
      end

      # 新增付费率 = 新增充值人数 / 新增注册数
      new_payment_rate = if aggregated.new_registrations > 0 do
        Decimal.div(Decimal.new(aggregated.new_paying_users), Decimal.new(aggregated.new_registrations))
        |> Decimal.mult(Decimal.new(100))
      else
        Decimal.new(0)
      end

      # 有效付费率 = 充值人数 / 有效活跃用户数
      valid_payment_rate = if aggregated.valid_active_users > 0 do
        Decimal.div(Decimal.new(aggregated.paying_users), Decimal.new(aggregated.valid_active_users))
        |> Decimal.mult(Decimal.new(100))
      else
        Decimal.new(0)
      end

      # 有效新增付费率 = 新增充值人数 / 有效新增用户数
      valid_new_payment_rate = if aggregated.valid_new_users > 0 do
        Decimal.div(Decimal.new(aggregated.new_paying_users), Decimal.new(aggregated.valid_new_users))
        |> Decimal.mult(Decimal.new(100))
      else
        Decimal.new(0)
      end

      # 退充比 = 提现总额 / 充值总额
      withdrawal_recharge_ratio = if Decimal.compare(aggregated.total_recharge_amount, Decimal.new(0)) == :gt do
        Decimal.div(aggregated.withdrawal_amount, aggregated.total_recharge_amount)
        |> Decimal.mult(Decimal.new(100))
      else
        Decimal.new(0)
      end

      # ARPU = 总收入 / 活跃用户数
      arpu = if aggregated.active_users > 0 do
        Decimal.div(aggregated.total_recharge_amount, Decimal.new(aggregated.active_users))
      else
        Decimal.new(0)
      end

      # ARPPU = 总收入 / 付费用户数
      arppu = if aggregated.paying_users > 0 do
        Decimal.div(aggregated.total_recharge_amount, Decimal.new(aggregated.paying_users))
      else
        Decimal.new(0)
      end

      # 有效ARPU = 总收入 / 有效活跃用户数
      valid_arpu = if aggregated.valid_active_users > 0 do
        Decimal.div(aggregated.total_recharge_amount, Decimal.new(aggregated.valid_active_users))
      else
        Decimal.new(0)
      end

      %{
        payment_rate: payment_rate,
        new_payment_rate: new_payment_rate,
        valid_payment_rate: valid_payment_rate,
        valid_new_payment_rate: valid_new_payment_rate,
        withdrawal_recharge_ratio: withdrawal_recharge_ratio,
        arpu: arpu,
        arppu: arppu,
        valid_arpu: valid_arpu
      }
    rescue
      _ ->
        %{
          payment_rate: Decimal.new(0),
          new_payment_rate: Decimal.new(0),
          valid_payment_rate: Decimal.new(0),
          valid_new_payment_rate: Decimal.new(0),
          withdrawal_recharge_ratio: Decimal.new(0),
          arpu: Decimal.new(0),
          arppu: Decimal.new(0),
          valid_arpu: Decimal.new(0)
        }
    end
  end

  # 计算系统级别的留存率（各渠道平均值）
  defp calculate_system_retention(channel_reports) do
    try do
      if Enum.empty?(channel_reports) do
        %{
          next_day_retention: Decimal.new(0),
          day3_retention: Decimal.new(0),
          day7_retention: Decimal.new(0),
          day14_retention: Decimal.new(0),
          day30_retention: Decimal.new(0)
        }
      else
        count = length(channel_reports)

        # 计算各留存率的平均值
        totals = Enum.reduce(channel_reports, %{
          next_day: Decimal.new(0),
          day3: Decimal.new(0),
          day7: Decimal.new(0),
          day14: Decimal.new(0),
          day30: Decimal.new(0)
        }, fn report, acc ->
          %{
            next_day: Decimal.add(acc.next_day, report.next_day_retention || Decimal.new(0)),
            day3: Decimal.add(acc.day3, report.day3_retention || Decimal.new(0)),
            day7: Decimal.add(acc.day7, report.day7_retention || Decimal.new(0)),
            day14: Decimal.add(acc.day14, report.day14_retention || Decimal.new(0)),
            day30: Decimal.add(acc.day30, report.day30_retention || Decimal.new(0))
          }
        end)

        %{
          next_day_retention: Decimal.div(totals.next_day, Decimal.new(count)),
          day3_retention: Decimal.div(totals.day3, Decimal.new(count)),
          day7_retention: Decimal.div(totals.day7, Decimal.new(count)),
          day14_retention: Decimal.div(totals.day14, Decimal.new(count)),
          day30_retention: Decimal.div(totals.day30, Decimal.new(count))
        }
      end
    rescue
      _ ->
        %{
          next_day_retention: Decimal.new(0),
          day3_retention: Decimal.new(0),
          day7_retention: Decimal.new(0),
          day14_retention: Decimal.new(0),
          day30_retention: Decimal.new(0)
        }
    end
  end

  # 计算系统级别的平均会话时长
  defp calculate_system_avg_session_time(channel_reports) do
    try do
      if Enum.empty?(channel_reports) do
        Decimal.new(0)
      else
        valid_reports = Enum.filter(channel_reports, fn report ->
          report.avg_session_time != nil && Decimal.compare(report.avg_session_time, Decimal.new(0)) == :gt
        end)

        if Enum.empty?(valid_reports) do
          Decimal.new(0)
        else
          total_time = Enum.reduce(valid_reports, Decimal.new(0), fn report, acc ->
            Decimal.add(acc, report.avg_session_time)
          end)

          Decimal.div(total_time, Decimal.new(length(valid_reports)))
        end
      end
    rescue
      _ -> Decimal.new(0)
    end
  end

  # 生成月度汇总报表
  defp generate_monthly_summary(start_date, end_date) do
    try do
      # 这里应该获取该月每日的报表数据并汇总
      # 简化处理，返回基础结构
      %{
        id: Ash.UUID.generate(),
        report_date: start_date,
        report_type: :monthly,
        total_channels: 0,
        active_channels: 0,
        new_devices: 0,
        new_registrations: 0,
        valid_new_users: 0,
        mobile_bound_registrations: 0,
        active_users: 0,
        valid_active_users: 0,
        active_mobile_bound: 0,
        online_users: 0,
        paying_users: 0,
        new_paying_users: 0,
        first_time_payers: 0,
        total_recharge_amount: Decimal.new(0),
        new_recharge_amount: Decimal.new(0),
        withdrawal_amount: Decimal.new(0),
        withdrawal_users: 0,
        withdrawal_recharge_ratio: Decimal.new(0),
        payment_rate: Decimal.new(0),
        new_payment_rate: Decimal.new(0),
        valid_payment_rate: Decimal.new(0),
        valid_new_payment_rate: Decimal.new(0),
        arpu: Decimal.new(0),
        arppu: Decimal.new(0),
        valid_arpu: Decimal.new(0),
        next_day_retention: Decimal.new(0),
        day3_retention: Decimal.new(0),
        day7_retention: Decimal.new(0),
        day14_retention: Decimal.new(0),
        day30_retention: Decimal.new(0),
        total_games_played: 0,
        games_played_today: 0,
        avg_session_time: Decimal.new(0),
        report_data: %{
          period: "#{start_date} - #{end_date}",
          type: "monthly_summary"
        },
        inserted_at: DateTime.utc_now(),
        updated_at: DateTime.utc_now()
      }
    rescue
      _ ->
        %{
          id: Ash.UUID.generate(),
          report_date: start_date,
          report_type: :monthly,
          total_channels: 0,
          active_channels: 0,
          report_data: %{error: "生成月度报表失败"},
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        }
    end
  end

  # 计算在线统计
  defp calculate_online_statistics do
    try do
      # 获取当前在线用户统计
      online_users = get_total_online_users()
      active_rooms = get_active_game_rooms()

      %{
        id: Ash.UUID.generate(),
        report_date: Date.utc_today(),
        report_type: :online,
        online_users: online_users,
        active_rooms: active_rooms,
        report_data: %{
          timestamp: DateTime.utc_now(),
          type: "online_statistics"
        },
        inserted_at: DateTime.utc_now(),
        updated_at: DateTime.utc_now()
      }
    rescue
      _ ->
        %{
          id: Ash.UUID.generate(),
          report_date: Date.utc_today(),
          report_type: :online,
          online_users: 0,
          report_data: %{error: "获取在线统计失败"},
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        }
    end
  end

  # 计算实时数据
  defp calculate_realtime_data do
    try do
      # 获取实时数据
      current_time = DateTime.utc_now()
      today = Date.utc_today()

      # 获取详细的游戏统计
      game_details = get_today_game_details()

      %{
        id: Ash.UUID.generate(),
        report_date: today,
        report_type: :realtime,
        online_users: get_total_online_users(),
        new_registrations: get_today_registrations(),
        new_recharge_amount: get_today_recharge_amount(),
        games_played_today: game_details.total_games,
        total_games_played: get_total_games_count(),
        active_users: game_details.active_game_users,
        report_data: %{
          timestamp: current_time,
          type: "realtime_data",
          last_update: current_time,
          game_details: game_details,
          active_game_users: game_details.active_game_users,
          games_by_type: game_details.games_by_type,
          game_win_rate: game_details.win_rate,
          total_bet_amount: game_details.total_bet_amount,
          total_win_amount: game_details.total_win_amount
        },
        inserted_at: current_time,
        updated_at: current_time
      }
    rescue
      _ ->
        %{
          id: Ash.UUID.generate(),
          report_date: Date.utc_today(),
          report_type: :realtime,
          online_users: 0,
          games_played_today: 0,
          total_games_played: 0,
          report_data: %{error: "获取实时数据失败"},
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        }
    end
  end

  # 获取总在线用户数
  defp get_total_online_users do
    try do
      case Cypridina.Accounts.User.read() do
        {:ok, users} ->
          users
          |> Enum.count(fn user -> is_nil(user.last_offline_at) end)

        {:error, _} -> 0
      end
    rescue
      _ -> 0
    end
  end

  # 获取活跃游戏房间数
  defp get_active_game_rooms do
    try do
      # 这里应该查询活跃的游戏房间
      # 简化处理
      10
    rescue
      _ -> 0
    end
  end

  # 获取今日注册数
  defp get_today_registrations do
    try do
      today_start = DateTime.new!(Date.utc_today(), ~T[00:00:00])
      today_end = DateTime.new!(Date.utc_today(), ~T[23:59:59])

      case Cypridina.Accounts.User.read() do
        {:ok, users} ->
          users
          |> Enum.count(fn user ->
            user.inserted_at >= today_start && user.inserted_at <= today_end
          end)

        {:error, _} -> 0
      end
    rescue
      _ -> 0
    end
  end

  # 获取今日充值金额
  defp get_today_recharge_amount do
    try do
      today_start = DateTime.new!(Date.utc_today(), ~T[00:00:00])
      today_end = DateTime.new!(Date.utc_today(), ~T[23:59:59])

      recharge_query = Teen.PaymentSystem.RechargeRecord
      |> Ash.Query.filter(expr(status == 1 and inserted_at >= ^today_start and inserted_at <= ^today_end))

      case Ash.read(recharge_query) do
        {:ok, records} ->
          records
          |> Enum.reduce(Decimal.new(0), fn record, acc ->
            Decimal.add(acc, record.amount || Decimal.new(0))
          end)

        {:error, _} -> Decimal.new(0)
      end
    rescue
      _ -> Decimal.new(0)
    end
  end

  # 获取今日游戏局数
  defp get_today_games_count do
    try do
      today_start = DateTime.new!(Date.utc_today(), ~T[00:00:00])
      today_end = DateTime.new!(Date.utc_today(), ~T[23:59:59])

      # 查询今日的游戏记录，排除取消的游戏
      today_games_query = Teen.GameManagement.GameRecord
      |> Ash.Query.filter(
        expr(inserted_at >= ^today_start and
        inserted_at <= ^today_end and
        result_status != :cancelled)
      )

      case Ash.read(today_games_query) do
        {:ok, game_records} ->
          length(game_records)

        {:error, _} ->
          0
      end
    rescue
      _ -> 0
    end
  end

  # 获取总游戏局数
  defp get_total_games_count do
    try do
      # 查询所有游戏记录，排除取消的游戏
      total_games_query = Teen.GameManagement.GameRecord
      |> Ash.Query.filter(expr(result_status != :cancelled))

      case Ash.read(total_games_query) do
        {:ok, game_records} ->
          length(game_records)

        {:error, _} ->
          0
      end
    rescue
      _ -> 0
    end
  end

  # 获取今日活跃游戏用户数
  defp get_today_active_game_users do
    try do
      today_start = DateTime.new!(Date.utc_today(), ~T[00:00:00])
      today_end = DateTime.new!(Date.utc_today(), ~T[23:59:59])

      # 查询今日有游戏记录的用户
      today_games_query = Teen.GameManagement.GameRecord
      |> Ash.Query.filter(
        expr(inserted_at >= ^today_start and
        inserted_at <= ^today_end and
        result_status != :cancelled)
      )

      case Ash.read(today_games_query) do
        {:ok, game_records} ->
          game_records
          |> Enum.map(& &1.user_id)
          |> Enum.uniq()
          |> length()

        {:error, _} ->
          0
      end
    rescue
      _ -> 0
    end
  end

  # 获取今日游戏详细统计
  defp get_today_game_details do
    try do
      today_start = DateTime.new!(Date.utc_today(), ~T[00:00:00])
      today_end = DateTime.new!(Date.utc_today(), ~T[23:59:59])

      # 查询今日的游戏记录
      today_games_query = Teen.GameManagement.GameRecord
      |> Ash.Query.filter(
        expr(inserted_at >= ^today_start and
        inserted_at <= ^today_end and
        result_status != :cancelled)
      )

      case Ash.read(today_games_query) do
        {:ok, game_records} ->
          total_games = length(game_records)

          # 按游戏类型分组统计
          games_by_type = game_records
          |> Enum.group_by(& &1.game_type)
          |> Enum.map(fn {type, records} -> {type, length(records)} end)
          |> Enum.into(%{})

          # 胜负统计
          win_games = Enum.count(game_records, & &1.result_status == :win)
          lose_games = Enum.count(game_records, & &1.result_status == :lose)
          draw_games = Enum.count(game_records, & &1.result_status == :draw)
          in_progress_games = Enum.count(game_records, & &1.result_status == :in_progress)

          # 活跃游戏用户数
          active_game_users = game_records
          |> Enum.map(& &1.user_id)
          |> Enum.uniq()
          |> length()

          # 总下注金额
          total_bet_amount = game_records
          |> Enum.reduce(Decimal.new(0), fn record, acc ->
            Decimal.add(acc, record.bet_amount || Decimal.new(0))
          end)

          # 总赢得金额
          total_win_amount = game_records
          |> Enum.reduce(Decimal.new(0), fn record, acc ->
            Decimal.add(acc, record.win_amount || Decimal.new(0))
          end)

          %{
            total_games: total_games,
            games_by_type: games_by_type,
            win_games: win_games,
            lose_games: lose_games,
            draw_games: draw_games,
            in_progress_games: in_progress_games,
            active_game_users: active_game_users,
            total_bet_amount: total_bet_amount,
            total_win_amount: total_win_amount,
            win_rate: (if total_games > 0, do: Float.round(win_games / total_games * 100, 2), else: 0.0)
          }

        {:error, _} ->
          %{
            total_games: 0,
            games_by_type: %{},
            win_games: 0,
            lose_games: 0,
            draw_games: 0,
            in_progress_games: 0,
            active_game_users: 0,
            total_bet_amount: Decimal.new(0),
            total_win_amount: Decimal.new(0),
            win_rate: 0.0
          }
      end
    rescue
      _ ->
        %{
          total_games: 0,
          games_by_type: %{},
          win_games: 0,
          lose_games: 0,
          draw_games: 0,
          in_progress_games: 0,
          active_game_users: 0,
          total_bet_amount: Decimal.new(0),
          total_win_amount: Decimal.new(0),
          win_rate: 0.0
        }
    end
  end

  # ==================== 综合指标计算函数 ====================

  # 计算综合指标数据
  defp calculate_comprehensive_metrics(report_date) do
    try do
      # 获取基础数据
      user_metrics = get_user_metrics(report_date)
      device_metrics = get_device_metrics(report_date)
      payment_metrics = get_payment_metrics(report_date)
      withdrawal_metrics = get_withdrawal_metrics(report_date)
      retention_metrics = get_retention_metrics(report_date)
      game_metrics = get_game_metrics(report_date)

      # 计算综合指标
      comprehensive_data = %{
        id: Ash.UUID.generate(),
        report_date: report_date,
        report_type: :comprehensive,

        # 设备和用户指标
        new_devices: device_metrics.new_devices,
        new_registrations: user_metrics.new_registrations,
        valid_new_users: user_metrics.valid_new_users,
        mobile_bound_registrations: user_metrics.mobile_bound_registrations,
        active_users: user_metrics.active_users,
        valid_active_users: user_metrics.valid_active_users,
        active_mobile_bound: user_metrics.active_mobile_bound,
        online_users: user_metrics.online_users,

        # 充值指标
        paying_users: payment_metrics.paying_users,
        new_paying_users: payment_metrics.new_paying_users,
        first_time_payers: payment_metrics.first_time_payers,
        total_recharge_amount: payment_metrics.total_recharge_amount,
        new_recharge_amount: payment_metrics.new_recharge_amount,

        # 提现指标
        withdrawal_amount: withdrawal_metrics.withdrawal_amount,
        withdrawal_users: withdrawal_metrics.withdrawal_users,

        # 比率指标
        withdrawal_recharge_ratio: calculate_withdrawal_recharge_ratio(
          withdrawal_metrics.withdrawal_amount,
          payment_metrics.total_recharge_amount
        ),
        payment_rate: calculate_payment_rate(payment_metrics.paying_users, user_metrics.active_users),
        new_payment_rate: calculate_payment_rate(payment_metrics.new_paying_users, user_metrics.new_registrations),
        valid_payment_rate: calculate_payment_rate(payment_metrics.paying_users, user_metrics.valid_active_users),
        valid_new_payment_rate: calculate_payment_rate(payment_metrics.new_paying_users, user_metrics.valid_new_users),

        # ARPU指标
        arpu: calculate_arpu(payment_metrics.total_recharge_amount, user_metrics.active_users),
        arppu: calculate_arppu(payment_metrics.total_recharge_amount, payment_metrics.paying_users),
        valid_arpu: calculate_arpu(payment_metrics.total_recharge_amount, user_metrics.valid_active_users),

        # 留存率指标
        next_day_retention: retention_metrics.next_day_retention,
        day3_retention: retention_metrics.day3_retention,
        day7_retention: retention_metrics.day7_retention,
        day14_retention: retention_metrics.day14_retention,
        day30_retention: retention_metrics.day30_retention,

        # 游戏指标
        total_games_played: game_metrics.total_games_played,
        games_played_today: game_metrics.games_played_today,
        avg_session_time: game_metrics.avg_session_time,

        # 详细数据
        report_data: %{
          user_metrics: user_metrics,
          device_metrics: device_metrics,
          payment_metrics: payment_metrics,
          withdrawal_metrics: withdrawal_metrics,
          retention_metrics: retention_metrics,
          game_metrics: game_metrics,
          calculated_at: DateTime.utc_now()
        },

        inserted_at: DateTime.utc_now(),
        updated_at: DateTime.utc_now()
      }

      comprehensive_data
    rescue
      error ->
        %{
          id: Ash.UUID.generate(),
          report_date: report_date,
          report_type: :comprehensive,
          error: "计算综合指标失败: #{inspect(error)}",
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        }
    end
  end

  # 获取用户指标
  defp get_user_metrics(report_date) do
    try do
      day_start = DateTime.new!(report_date, ~T[00:00:00])
      day_end = DateTime.new!(report_date, ~T[23:59:59])

      # 新增注册用户
      new_users = get_new_users(day_start, day_end)

      # 手机绑定用户
      mobile_bound_users = new_users
      |> Enum.filter(fn user ->
        not is_nil(user.phone_verified_at) and user.phone_verified_at != ""
      end)

      # 活跃用户（今日有登录记录的用户）
      active_users = get_active_users(report_date)

      # 有效新增用户（注册后有活动的用户）
      new_user_ids = Enum.map(new_users, & &1.id)
      valid_new_users = Enum.filter(active_users, &(&1 in new_user_ids))

      # 有效活跃用户（活跃且有充值或游戏行为的用户）
      valid_active_users = get_valid_active_users(active_users, day_start, day_end)

      # 活跃手机绑定用户
      active_mobile_bound = get_active_mobile_bound_users(active_users)

      # 在线用户
      online_users = get_total_online_users()

      %{
        new_registrations: length(new_users),
        mobile_bound_registrations: length(mobile_bound_users),
        active_users: length(active_users),
        valid_new_users: length(valid_new_users),
        valid_active_users: length(valid_active_users),
        active_mobile_bound: length(active_mobile_bound),
        online_users: online_users
      }
    rescue
      error ->
        Logger.error("获取用户指标失败: #{inspect(error)}")
        %{
          new_registrations: 0,
          mobile_bound_registrations: 0,
          active_users: 0,
          valid_new_users: 0,
          valid_active_users: 0,
          active_mobile_bound: 0,
          online_users: 0
        }
    end
  end

  # 获取新增用户的辅助函数
  defp get_new_users(day_start, day_end) do
    try do
      new_users_query = Cypridina.Accounts.User
      |> Ash.Query.filter(expr(inserted_at >= ^day_start and inserted_at <= ^day_end))

      case Ash.read(new_users_query) do
        {:ok, users} -> users
        {:error, error} ->
          Logger.warning("查询新增用户失败: #{inspect(error)}")
          []
      end
    rescue
      error ->
        Logger.error("获取新增用户异常: #{inspect(error)}")
        []
    end
  end

  # 获取活跃用户的辅助函数
  defp get_active_users(report_date) do
    try do
      # 首先尝试从登录记录获取
      if Code.ensure_loaded?(Teen.ActivitySystem.UserLoginRecord) do
        active_users_query = Teen.ActivitySystem.UserLoginRecord
        |> Ash.Query.filter(expr(login_date == ^report_date))

        case Ash.read(active_users_query) do
          {:ok, records} ->
            records
            |> Enum.map(& &1.user_id)
            |> Enum.uniq()
          {:error, error} ->
            Logger.warning("从登录记录查询活跃用户失败: #{inspect(error)}")
            get_active_users_fallback(report_date)
        end
      else
        Logger.warning("UserLoginRecord 模块不存在，使用备用方法")
        get_active_users_fallback(report_date)
      end
    rescue
      error ->
        Logger.error("获取活跃用户异常: #{inspect(error)}")
        get_active_users_fallback(report_date)
    end
  end

  # 活跃用户备用查询方法
  defp get_active_users_fallback(report_date) do
    try do
      day_start = DateTime.new!(report_date, ~T[00:00:00])
      day_end = DateTime.new!(report_date, ~T[23:59:59])

      # 从游戏记录获取活跃用户
      game_active_users = get_active_game_users_by_date(day_start, day_end)

      # 从充值记录获取活跃用户
      payment_active_users = get_active_payment_users(day_start, day_end)

      # 合并去重
      (game_active_users ++ payment_active_users)
      |> Enum.uniq()
    rescue
      error ->
        Logger.error("备用活跃用户查询失败: #{inspect(error)}")
        []
    end
  end

  # 从游戏记录获取活跃用户
  defp get_active_game_users_by_date(day_start, day_end) do
    try do
      if Code.ensure_loaded?(Teen.GameManagement.GameRecord) do
        game_query = Teen.GameManagement.GameRecord
        |> Ash.Query.filter(expr(inserted_at >= ^day_start and inserted_at <= ^day_end))

        case Ash.read(game_query) do
          {:ok, records} ->
            records
            |> Enum.map(& &1.user_id)
            |> Enum.uniq()
          {:error, _} -> []
        end
      else
        []
      end
    rescue
      _ -> []
    end
  end

  # 从充值记录获取活跃用户
  defp get_active_payment_users(day_start, day_end) do
    try do
      if Code.ensure_loaded?(Teen.PaymentSystem.RechargeRecord) do
        payment_query = Teen.PaymentSystem.RechargeRecord
        |> Ash.Query.filter(expr(inserted_at >= ^day_start and inserted_at <= ^day_end))

        case Ash.read(payment_query) do
          {:ok, records} ->
            records
            |> Enum.map(& &1.user_id)
            |> Enum.uniq()
          {:error, _} -> []
        end
      else
        []
      end
    rescue
      _ -> []
    end
  end

  # 获取设备指标
  defp get_device_metrics(report_date) do
    try do
      day_start = DateTime.new!(report_date, ~T[00:00:00])
      day_end = DateTime.new!(report_date, ~T[23:59:59])

      # 新增设备
      new_devices_query = Cypridina.Accounts.UserDevice
      |> Ash.Query.filter(expr(inserted_at >= ^day_start and inserted_at <= ^day_end))

      new_devices = case Ash.read(new_devices_query) do
        {:ok, devices} -> devices
        {:error, _} -> []
      end

      %{
        new_devices: length(new_devices)
      }
    rescue
      _ ->
        %{new_devices: 0}
    end
  end

  # 获取支付指标
  defp get_payment_metrics(report_date) do
    try do
      day_start = DateTime.new!(report_date, ~T[00:00:00])
      day_end = DateTime.new!(report_date, ~T[23:59:59])

      # 获取当日充值记录
      recharge_records = get_recharge_records(day_start, day_end)

      # 充值用户
      paying_users = recharge_records
      |> Enum.map(& &1.user_id)
      |> Enum.filter(&(&1 != nil))
      |> Enum.uniq()

      # 新增充值用户（首次充值在当日的用户）
      new_paying_users = get_new_paying_users_safe(paying_users, day_end)

      # 首付用户（历史上第一次充值的用户）
      first_time_payers = get_first_time_payers_safe(recharge_records)

      # 总充值金额
      total_recharge_amount = calculate_total_recharge_amount(recharge_records)

      # 新增充值额（新用户的充值金额）
      new_recharge_amount = calculate_new_recharge_amount(recharge_records, new_paying_users)

      %{
        paying_users: length(paying_users),
        new_paying_users: length(new_paying_users),
        first_time_payers: length(first_time_payers),
        total_recharge_amount: total_recharge_amount,
        new_recharge_amount: new_recharge_amount
      }
    rescue
      error ->
        Logger.error("获取支付指标失败: #{inspect(error)}")
        %{
          paying_users: 0,
          new_paying_users: 0,
          first_time_payers: 0,
          total_recharge_amount: Decimal.new(0),
          new_recharge_amount: Decimal.new(0)
        }
    end
  end

  # 获取充值记录的辅助函数
  defp get_recharge_records(day_start, day_end) do
    try do
      if Code.ensure_loaded?(Teen.PaymentSystem.RechargeRecord) do
        recharge_query = Teen.PaymentSystem.RechargeRecord
        |> Ash.Query.filter(
          expr(inserted_at >= ^day_start and
          inserted_at <= ^day_end and
          status == :completed)
        )

        case Ash.read(recharge_query) do
          {:ok, records} -> records
          {:error, error} ->
            Logger.warning("查询充值记录失败: #{inspect(error)}")
            []
        end
      else
        Logger.warning("RechargeRecord 模块不存在")
        []
      end
    rescue
      error ->
        Logger.error("获取充值记录异常: #{inspect(error)}")
        []
    end
  end

  # 计算总充值金额
  defp calculate_total_recharge_amount(recharge_records) do
    try do
      recharge_records
      |> Enum.reduce(Decimal.new(0), fn record, acc ->
        amount = case record.amount do
          %Decimal{} = decimal -> decimal
          amount when is_number(amount) -> Decimal.new(amount)
          _ -> Decimal.new(0)
        end
        Decimal.add(acc, amount)
      end)
    rescue
      error ->
        Logger.error("计算总充值金额失败: #{inspect(error)}")
        Decimal.new(0)
    end
  end

  # 计算新增充值金额
  defp calculate_new_recharge_amount(recharge_records, new_paying_users) do
    try do
      recharge_records
      |> Enum.filter(&(&1.user_id in new_paying_users))
      |> Enum.reduce(Decimal.new(0), fn record, acc ->
        amount = case record.amount do
          %Decimal{} = decimal -> decimal
          amount when is_number(amount) -> Decimal.new(amount)
          _ -> Decimal.new(0)
        end
        Decimal.add(acc, amount)
      end)
    rescue
      error ->
        Logger.error("计算新增充值金额失败: #{inspect(error)}")
        Decimal.new(0)
    end
  end

  # 获取提现指标
  defp get_withdrawal_metrics(report_date) do
    try do
      day_start = DateTime.new!(report_date, ~T[00:00:00])
      day_end = DateTime.new!(report_date, ~T[23:59:59])

      # 获取当日提现记录
      withdrawal_records = get_withdrawal_records(day_start, day_end)

      # 提现用户
      withdrawal_users = withdrawal_records
      |> Enum.map(& &1.user_id)
      |> Enum.filter(&(&1 != nil))
      |> Enum.uniq()

      # 总提现金额
      withdrawal_amount = calculate_total_withdrawal_amount(withdrawal_records)

      %{
        withdrawal_users: length(withdrawal_users),
        withdrawal_amount: withdrawal_amount
      }
    rescue
      error ->
        Logger.error("获取提现指标失败: #{inspect(error)}")
        %{
          withdrawal_users: 0,
          withdrawal_amount: Decimal.new(0)
        }
    end
  end

  # 获取提现记录的辅助函数
  defp get_withdrawal_records(day_start, day_end) do
    try do
      if Code.ensure_loaded?(Teen.PaymentSystem.WithdrawalRecord) do
        withdrawal_query = Teen.PaymentSystem.WithdrawalRecord
        |> Ash.Query.filter(
          expr(inserted_at >= ^day_start and
          inserted_at <= ^day_end and
          result_status == 1)
        )

        case Ash.read(withdrawal_query) do
          {:ok, records} -> records
          {:error, error} ->
            Logger.warning("查询提现记录失败: #{inspect(error)}")
            []
        end
      else
        Logger.warning("WithdrawalRecord 模块不存在")
        []
      end
    rescue
      error ->
        Logger.error("获取提现记录异常: #{inspect(error)}")
        []
    end
  end

  # 计算总提现金额
  defp calculate_total_withdrawal_amount(withdrawal_records) do
    try do
      withdrawal_records
      |> Enum.reduce(Decimal.new(0), fn record, acc ->
        amount = case record.withdrawal_amount do
          %Decimal{} = decimal -> decimal
          amount when is_number(amount) -> Decimal.new(amount)
          _ -> Decimal.new(0)
        end
        Decimal.add(acc, amount)
      end)
    rescue
      error ->
        Logger.error("计算总提现金额失败: #{inspect(error)}")
        Decimal.new(0)
    end
  end

  # 获取留存率指标
  defp get_retention_metrics(report_date) do
    try do
      # 获取基准用户群（在指定日期注册的用户）
      day_start = DateTime.new!(report_date, ~T[00:00:00])
      day_end = DateTime.new!(report_date, ~T[23:59:59])

      base_users_query = Cypridina.Accounts.User
      |> Ash.Query.filter(expr(inserted_at >= ^day_start and inserted_at <= ^day_end))

      base_users = case Ash.read(base_users_query) do
        {:ok, users} -> users
        {:error, _} -> []
      end

      base_user_ids = Enum.map(base_users, & &1.id)

      %{
        next_day_retention: calculate_retention_rate(base_user_ids, report_date, 1),
        day3_retention: calculate_retention_rate(base_user_ids, report_date, 3),
        day7_retention: calculate_retention_rate(base_user_ids, report_date, 7),
        day14_retention: calculate_retention_rate(base_user_ids, report_date, 14),
        day30_retention: calculate_retention_rate(base_user_ids, report_date, 30),
        base_user_count: length(base_user_ids)
      }
    rescue
      _ ->
        %{
          next_day_retention: Decimal.new(0),
          day3_retention: Decimal.new(0),
          day7_retention: Decimal.new(0),
          day14_retention: Decimal.new(0),
          day30_retention: Decimal.new(0),
          base_user_count: 0
        }
    end
  end

  # 获取游戏指标
  defp get_game_metrics(report_date) do
    try do
      # 今日游戏数据
      today_game_details = get_today_game_details()

      # 总游戏局数
      total_games = get_total_games_count()

      # 平均会话时长（简化计算）
      avg_session_time = calculate_avg_session_time(report_date)

      %{
        total_games_played: total_games,
        games_played_today: today_game_details.total_games,
        avg_session_time: avg_session_time
      }
    rescue
      _ ->
        %{
          total_games_played: 0,
          games_played_today: 0,
          avg_session_time: Decimal.new(0)
        }
    end
  end

  # ==================== 辅助计算函数 ====================

  # 计算退充比
  defp calculate_withdrawal_recharge_ratio(withdrawal_amount, recharge_amount) do
    if Decimal.gt?(recharge_amount, 0) do
      Decimal.div(withdrawal_amount, recharge_amount)
      |> Decimal.mult(Decimal.new(100))
      |> Decimal.round(2)
    else
      Decimal.new(0)
    end
  end

  # 计算付费率
  defp calculate_payment_rate(paying_users, total_users) when total_users > 0 do
    Decimal.div(Decimal.new(paying_users), Decimal.new(total_users))
    |> Decimal.mult(Decimal.new(100))
    |> Decimal.round(2)
  end

  defp calculate_payment_rate(_, _), do: Decimal.new(0)

  # 计算ARPU
  defp calculate_arpu(total_revenue, total_users) when total_users > 0 do
    Decimal.div(total_revenue, Decimal.new(total_users))
    |> Decimal.round(2)
  end

  defp calculate_arpu(_, _), do: Decimal.new(0)

  # 计算ARPPU
  defp calculate_arppu(total_revenue, paying_users) when paying_users > 0 do
    Decimal.div(total_revenue, Decimal.new(paying_users))
    |> Decimal.round(2)
  end

  defp calculate_arppu(_, _), do: Decimal.new(0)

  # 计算留存率
  defp calculate_retention_rate(base_user_ids, base_date, days) when length(base_user_ids) > 0 do
    try do
      retention_date = Date.add(base_date, days)

      # 查询在留存日期有活动的用户
      retention_query = Teen.ActivitySystem.UserLoginRecord
      |> Ash.Query.filter(expr(user_id in ^base_user_ids and login_date == ^retention_date))

      retained_users = case Ash.read(retention_query) do
        {:ok, records} ->
          records
          |> Enum.map(& &1.user_id)
          |> Enum.uniq()
        {:error, _} -> []
      end

      Decimal.div(Decimal.new(length(retained_users)), Decimal.new(length(base_user_ids)))
      |> Decimal.mult(Decimal.new(100))
      |> Decimal.round(2)
    rescue
      _ -> Decimal.new(0)
    end
  end

  defp calculate_retention_rate(_, _, _), do: Decimal.new(0)

  # 获取新增充值用户（安全版本）
  defp get_new_paying_users_safe(current_paying_users, end_date) do
    try do
      if Code.ensure_loaded?(Teen.PaymentSystem.RechargeRecord) do
        Enum.filter(current_paying_users, fn user_id ->
          # 查询该用户在end_date之前的所有充值记录
          historical_query = Teen.PaymentSystem.RechargeRecord
          |> Ash.Query.filter(expr(user_id == ^user_id and inserted_at <= ^end_date and status == :completed))

          case Ash.read(historical_query) do
            {:ok, records} ->
              # 如果只有一条记录，说明是新增充值用户
              length(records) == 1
            {:error, _} -> false
          end
        end)
      else
        Logger.warning("RechargeRecord 模块不存在，无法计算新增充值用户")
        []
      end
    rescue
      error ->
        Logger.error("获取新增充值用户失败: #{inspect(error)}")
        []
    end
  end

  # 获取新增充值用户（兼容旧版本）
  defp get_new_paying_users(current_paying_users, end_date) do
    get_new_paying_users_safe(current_paying_users, end_date)
  end

  # 获取首付用户（安全版本）
  defp get_first_time_payers_safe(recharge_records) do
    try do
      if length(recharge_records) == 0 do
        []
      else
        # 按用户分组，找出每个用户的首次充值
        user_first_recharges = recharge_records
        |> Enum.group_by(& &1.user_id)
        |> Enum.map(fn {user_id, records} ->
          # 找出最早的充值记录
          first_record = Enum.min_by(records, fn record ->
            case record.inserted_at do
              %DateTime{} = dt -> dt
              _ -> DateTime.utc_now()
            end
          end)
          {user_id, first_record}
        end)

        # 返回首次充值的用户ID列表
        Enum.map(user_first_recharges, fn {user_id, _} -> user_id end)
        |> Enum.filter(&(&1 != nil))
      end
    rescue
      error ->
        Logger.error("获取首付用户失败: #{inspect(error)}")
        []
    end
  end

  # 获取首付用户（兼容旧版本）
  defp get_first_time_payers(recharge_records) do
    get_first_time_payers_safe(recharge_records)
  end

  # 获取有效活跃用户
  defp get_valid_active_users(active_user_ids, day_start, day_end) do
    try do
      # 查询这些用户在时间范围内是否有充值记录
      recharge_query = Teen.PaymentSystem.RechargeRecord
      |> Ash.Query.filter(expr(user_id in ^active_user_ids and
                               inserted_at >= ^day_start and
                               inserted_at <= ^day_end and
                               status == :completed))

      recharge_users = case Ash.read(recharge_query) do
        {:ok, records} ->
          records
          |> Enum.map(& &1.user_id)
          |> Enum.uniq()
        {:error, _} -> []
      end

      # 查询这些用户在时间范围内是否有游戏记录
      game_users = get_active_game_users(active_user_ids, day_start, day_end)

      # 合并充值用户和游戏用户
      (recharge_users ++ game_users)
      |> Enum.uniq()
    rescue
      _ -> []
    end
  end

  # 获取活跃游戏用户
  defp get_active_game_users(user_ids, day_start, day_end) do
    try do
      if Code.ensure_loaded?(Teen.GameManagement.GameRecord) do
        game_query = Teen.GameManagement.GameRecord
        |> Ash.Query.filter(expr(user_id in ^user_ids and
                                 inserted_at >= ^day_start and
                                 inserted_at <= ^day_end))

        case Ash.read(game_query) do
          {:ok, records} ->
            records
            |> Enum.map(& &1.user_id)
            |> Enum.uniq()
          {:error, _} -> []
        end
      else
        []
      end
    rescue
      _ -> []
    end
  end

  # 获取活跃手机绑定用户
  defp get_active_mobile_bound_users(active_user_ids) do
    try do
      # 查询这些活跃用户中手机已验证的用户
      phone_bound_query = Cypridina.Accounts.User
      |> Ash.Query.filter(expr(id in ^active_user_ids and not is_nil(phone_verified_at)))

      case Ash.read(phone_bound_query) do
        {:ok, users} ->
          Enum.map(users, & &1.id)
        {:error, _} -> []
      end
    rescue
      _ -> []
    end
  end

  # 计算平均会话时长
  defp calculate_avg_session_time(_report_date) do
    # 简化实现，实际应该基于用户登录登出记录计算
    Decimal.new("45.5")
  end

  # ==================== 导出功能 ====================

  # 生成导出数据
  defp generate_export_data(start_date, end_date, format) do
    try do
      # 获取日期范围内的所有数据
      date_range = Date.range(start_date, end_date)

      report_data = Enum.map(date_range, fn date ->
        calculate_comprehensive_metrics(date)
      end)

      case format do
        :csv -> generate_csv_export(report_data)
        :excel -> generate_excel_export(report_data)
        :json -> generate_json_export(report_data)
        _ -> {:error, "不支持的导出格式"}
      end
    rescue
      error ->
        {:error, "导出数据生成失败: #{inspect(error)}"}
    end
  end

  # 生成CSV导出
  defp generate_csv_export(report_data) do
    try do
      headers = [
        "日期", "新增设备", "新增注册", "有效新增", "手机注册绑定", "活跃用户", "有效活跃",
        "活跃手机绑定", "充值人数", "新增充值人数", "首付人数", "新增充值额", "退出总额",
        "退出人数", "退充比", "付费率", "新增付费率", "有效付费率", "有效新增付费率",
        "ARPU", "ARPPU", "有效ARPU", "次留", "3留", "7留", "14留", "30留"
      ]

      csv_rows = Enum.map(report_data, fn data ->
        [
          Date.to_string(data.report_date),
          to_string(data.new_devices || 0),
          to_string(data.new_registrations || 0),
          to_string(data.valid_new_users || 0),
          to_string(data.mobile_bound_registrations || 0),
          to_string(data.active_users || 0),
          to_string(data.valid_active_users || 0),
          to_string(data.active_mobile_bound || 0),
          to_string(data.paying_users || 0),
          to_string(data.new_paying_users || 0),
          to_string(data.first_time_payers || 0),
          Decimal.to_string(data.new_recharge_amount || Decimal.new(0)),
          Decimal.to_string(data.withdrawal_amount || Decimal.new(0)),
          to_string(data.withdrawal_users || 0),
          Decimal.to_string(data.withdrawal_recharge_ratio || Decimal.new(0)),
          Decimal.to_string(data.payment_rate || Decimal.new(0)),
          Decimal.to_string(data.new_payment_rate || Decimal.new(0)),
          Decimal.to_string(data.valid_payment_rate || Decimal.new(0)),
          Decimal.to_string(data.valid_new_payment_rate || Decimal.new(0)),
          Decimal.to_string(data.arpu || Decimal.new(0)),
          Decimal.to_string(data.arppu || Decimal.new(0)),
          Decimal.to_string(data.valid_arpu || Decimal.new(0)),
          Decimal.to_string(data.next_day_retention || Decimal.new(0)),
          Decimal.to_string(data.day3_retention || Decimal.new(0)),
          Decimal.to_string(data.day7_retention || Decimal.new(0)),
          Decimal.to_string(data.day14_retention || Decimal.new(0)),
          Decimal.to_string(data.day30_retention || Decimal.new(0))
        ]
      end)

      csv_content = [headers | csv_rows]
      |> Enum.map(&Enum.join(&1, ","))
      |> Enum.join("\n")

      %{
        format: :csv,
        content: csv_content,
        filename: "comprehensive_report_#{Date.to_string(Date.utc_today())}.csv",
        content_type: "text/csv"
      }
    rescue
      error ->
        {:error, "CSV导出失败: #{inspect(error)}"}
    end
  end

  # 生成Excel导出
  defp generate_excel_export(report_data) do
    # Excel导出功能需要额外的库支持，这里提供基础结构
    %{
      format: :excel,
      content: "Excel导出功能需要额外实现",
      filename: "comprehensive_report_#{Date.to_string(Date.utc_today())}.xlsx",
      content_type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    }
  end

  # 生成JSON导出
  defp generate_json_export(report_data) do
    try do
      json_content = Jason.encode!(report_data)

      %{
        format: :json,
        content: json_content,
        filename: "comprehensive_report_#{Date.to_string(Date.utc_today())}.json",
        content_type: "application/json"
      }
    rescue
      error ->
        {:error, "JSON导出失败: #{inspect(error)}"}
    end
  end

  # ==================== 图表功能 ====================

  # 生成图表数据
  defp generate_chart_data(chart_type, start_date, end_date, metrics) do
    try do
      # 获取日期范围内的数据
      date_range = Date.range(start_date, end_date)

      report_data = Enum.map(date_range, fn date ->
        calculate_comprehensive_metrics(date)
      end)

      case chart_type do
        :line -> generate_line_chart(report_data, metrics)
        :bar -> generate_bar_chart(report_data, metrics)
        :pie -> generate_pie_chart(report_data, metrics)
        :area -> generate_area_chart(report_data, metrics)
        :funnel -> generate_funnel_chart(report_data, metrics)
        _ -> {:error, "不支持的图表类型"}
      end
    rescue
      error ->
        {:error, "图表数据生成失败: #{inspect(error)}"}
    end
  end

  # 生成折线图数据
  defp generate_line_chart(report_data, metrics) do
    labels = Enum.map(report_data, &Date.to_string(&1.report_date))

    datasets = Enum.map(metrics, fn metric ->
      data = Enum.map(report_data, &get_metric_value(&1, metric))

      %{
        label: get_metric_label(metric),
        data: data,
        borderColor: get_metric_color(metric),
        backgroundColor: get_metric_color(metric),
        fill: false
      }
    end)

    %{
      type: "line",
      data: %{
        labels: labels,
        datasets: datasets
      },
      options: %{
        responsive: true,
        plugins: %{
          title: %{
            display: true,
            text: "数据趋势图"
          }
        },
        scales: %{
          y: %{
            beginAtZero: true
          }
        }
      }
    }
  end

  # 生成柱状图数据
  defp generate_bar_chart(report_data, metrics) do
    labels = Enum.map(report_data, &Date.to_string(&1.report_date))

    datasets = Enum.map(metrics, fn metric ->
      data = Enum.map(report_data, &get_metric_value(&1, metric))

      %{
        label: get_metric_label(metric),
        data: data,
        backgroundColor: get_metric_color(metric)
      }
    end)

    %{
      type: "bar",
      data: %{
        labels: labels,
        datasets: datasets
      },
      options: %{
        responsive: true,
        plugins: %{
          title: %{
            display: true,
            text: "数据对比图"
          }
        },
        scales: %{
          y: %{
            beginAtZero: true
          }
        }
      }
    }
  end

  # 生成饼图数据
  defp generate_pie_chart(report_data, metrics) do
    # 使用最新数据生成饼图
    latest_data = List.first(report_data)

    if latest_data do
      data = Enum.map(metrics, &get_metric_value(latest_data, &1))
      labels = Enum.map(metrics, &get_metric_label/1)
      colors = Enum.map(metrics, &get_metric_color/1)

      %{
        type: "pie",
        data: %{
          labels: labels,
          datasets: [%{
            data: data,
            backgroundColor: colors
          }]
        },
        options: %{
          responsive: true,
          plugins: %{
            title: %{
              display: true,
              text: "数据分布图"
            }
          }
        }
      }
    else
      %{type: "pie", data: %{labels: [], datasets: []}}
    end
  end

  # 生成面积图数据
  defp generate_area_chart(report_data, metrics) do
    chart_data = generate_line_chart(report_data, metrics)

    # 修改为面积图
    updated_datasets = Enum.map(chart_data.data.datasets, fn dataset ->
      Map.put(dataset, :fill, true)
    end)

    chart_data
    |> put_in([:data, :datasets], updated_datasets)
    |> put_in([:type], "line")
    |> put_in([:options, :plugins, :title, :text], "面积图")
  end

  # 生成漏斗图数据
  defp generate_funnel_chart(report_data, metrics) do
    # 使用最新数据生成漏斗图
    latest_data = List.first(report_data)

    if latest_data do
      # 漏斗图通常用于展示转化流程
      funnel_data = [
        %{name: "新增注册", value: latest_data.new_registrations || 0},
        %{name: "有效新增", value: latest_data.valid_new_users || 0},
        %{name: "活跃用户", value: latest_data.active_users || 0},
        %{name: "充值用户", value: latest_data.paying_users || 0}
      ]

      %{
        type: "funnel",
        data: funnel_data,
        options: %{
          responsive: true,
          plugins: %{
            title: %{
              display: true,
              text: "用户转化漏斗"
            }
          }
        }
      }
    else
      %{type: "funnel", data: []}
    end
  end

  # ==================== 留存分析功能 ====================

  # 计算留存分析数据
  defp calculate_retention_analysis(cohort_date) do
    try do
      # 获取队列用户（在指定日期注册的用户）
      day_start = DateTime.new!(cohort_date, ~T[00:00:00])
      day_end = DateTime.new!(cohort_date, ~T[23:59:59])

      cohort_users_query = Cypridina.Accounts.User
      |> Ash.Query.filter(expr(inserted_at >= ^day_start and inserted_at <= ^day_end))

      cohort_users = case Ash.read(cohort_users_query) do
        {:ok, users} -> users
        {:error, _} -> []
      end

      cohort_user_ids = Enum.map(cohort_users, & &1.id)
      cohort_size = length(cohort_user_ids)

      if cohort_size > 0 do
        # 计算各天的留存情况
        retention_periods = [1, 3, 7, 14, 30]

        retention_data = Enum.map(retention_periods, fn days ->
          retention_date = Date.add(cohort_date, days)
          retained_count = count_retained_users(cohort_user_ids, retention_date)
          retention_rate = if cohort_size > 0, do: (retained_count / cohort_size * 100), else: 0

          %{
            day: days,
            retained_users: retained_count,
            retention_rate: Float.round(retention_rate, 2),
            cohort_size: cohort_size
          }
        end)

        %{
          cohort_date: cohort_date,
          cohort_size: cohort_size,
          retention_data: retention_data,
          analysis_date: Date.utc_today()
        }
      else
        %{
          cohort_date: cohort_date,
          cohort_size: 0,
          retention_data: [],
          analysis_date: Date.utc_today()
        }
      end
    rescue
      error ->
        %{
          cohort_date: cohort_date,
          error: "留存分析计算失败: #{inspect(error)}",
          analysis_date: Date.utc_today()
        }
    end
  end

  # 统计留存用户数量
  defp count_retained_users(cohort_user_ids, retention_date) do
    try do
      retention_query = Teen.ActivitySystem.UserLoginRecord
      |> Ash.Query.filter(expr(user_id in ^cohort_user_ids and login_date == ^retention_date))

      case Ash.read(retention_query) do
        {:ok, records} ->
          records
          |> Enum.map(& &1.user_id)
          |> Enum.uniq()
          |> length()
        {:error, _} -> 0
      end
    rescue
      _ -> 0
    end
  end

  # ==================== 图表辅助函数 ====================

  # 获取指标值
  defp get_metric_value(data, metric) do
    case Map.get(data, metric) do
      %Decimal{} = decimal -> Decimal.to_float(decimal)
      nil -> 0
      value when is_number(value) -> value
      _ -> 0
    end
  end

  # 获取指标标签
  defp get_metric_label(metric) do
    labels = %{
      new_devices: "新增设备",
      new_registrations: "新增注册",
      valid_new_users: "有效新增",
      mobile_bound_registrations: "手机注册绑定",
      active_users: "活跃用户",
      valid_active_users: "有效活跃",
      active_mobile_bound: "活跃手机绑定",
      paying_users: "充值人数",
      new_paying_users: "新增充值人数",
      first_time_payers: "首付人数",
      total_recharge_amount: "总充值额",
      new_recharge_amount: "新增充值额",
      withdrawal_amount: "退出总额",
      withdrawal_users: "退出人数",
      withdrawal_recharge_ratio: "退充比",
      payment_rate: "付费率",
      new_payment_rate: "新增付费率",
      valid_payment_rate: "有效付费率",
      valid_new_payment_rate: "有效新增付费率",
      arpu: "ARPU",
      arppu: "ARPPU",
      valid_arpu: "有效ARPU",
      next_day_retention: "次留",
      day3_retention: "3留",
      day7_retention: "7留",
      day14_retention: "14留",
      day30_retention: "30留"
    }

    Map.get(labels, metric, to_string(metric))
  end

  # 获取指标颜色
  defp get_metric_color(metric) do
    colors = %{
      new_devices: "#FF6384",
      new_registrations: "#36A2EB",
      valid_new_users: "#FFCE56",
      mobile_bound_registrations: "#4BC0C0",
      active_users: "#9966FF",
      valid_active_users: "#FF9F40",
      active_mobile_bound: "#FF6384",
      paying_users: "#C9CBCF",
      new_paying_users: "#4BC0C0",
      first_time_payers: "#36A2EB",
      total_recharge_amount: "#FFCE56",
      new_recharge_amount: "#9966FF",
      withdrawal_amount: "#FF6384",
      withdrawal_users: "#FF9F40",
      withdrawal_recharge_ratio: "#C9CBCF",
      payment_rate: "#4BC0C0",
      new_payment_rate: "#36A2EB",
      valid_payment_rate: "#FFCE56",
      valid_new_payment_rate: "#9966FF",
      arpu: "#FF9F40",
      arppu: "#FF6384",
      valid_arpu: "#C9CBCF",
      next_day_retention: "#4BC0C0",
      day3_retention: "#36A2EB",
      day7_retention: "#FFCE56",
      day14_retention: "#9966FF",
      day30_retention: "#FF9F40"
    }

    Map.get(colors, metric, "#999999")
  end
end
