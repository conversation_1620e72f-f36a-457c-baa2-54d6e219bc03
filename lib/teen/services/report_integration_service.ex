defmodule Teen.Services.ReportIntegrationService do
  @moduledoc """
  报表整合服务

  提供统一的报表管理接口，整合所有报表资源：
  - 统一的报表生成接口
  - 报表数据缓存管理
  - 报表调度和自动化
  - 报表性能监控
  """

  require Logger
  alias Teen.Statistics.{SystemReport, ChannelReport, SystemSummaryReport, UnifiedReportManager}
  alias Teen.Services.{ComprehensiveAnalyticsService, AnalyticsExportService}

  @doc """
  生成所有类型的报表
  """
  def generate_all_reports(date \\ Date.utc_today(), options \\ %{}) do
    Logger.info("开始生成所有报表，日期: #{date}")
    
    start_time = System.monotonic_time(:millisecond)
    
    try do
      # 使用统一报表管理器生成所有报表
      case UnifiedReportManager.generate_all_reports(date, options[:force_regenerate] || false) do
        {:ok, results} ->
          end_time = System.monotonic_time(:millisecond)
          duration = end_time - start_time
          
          Logger.info("所有报表生成完成，耗时: #{duration}ms")
          
          {:ok, %{
            results: results,
            summary: generate_results_summary(results),
            performance: %{
              duration_ms: duration,
              generated_at: DateTime.utc_now()
            }
          }}

        {:error, reason} ->
          Logger.error("生成所有报表失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      error ->
        Logger.error("生成报表过程中发生异常: #{inspect(error)}")
        {:error, "报表生成异常: #{inspect(error)}"}
    end
  end

  @doc """
  按类型生成报表
  """
  def generate_report_by_type(report_type, date \\ Date.utc_today(), options \\ %{}) do
    Logger.info("生成#{report_type}报表，日期: #{date}")
    
    case UnifiedReportManager.generate_report_by_type(report_type, date, options) do
      {:ok, result} ->
        Logger.info("#{report_type}报表生成完成")
        {:ok, result}

      {:error, reason} ->
        Logger.error("生成#{report_type}报表失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取报表仪表板数据
  """
  def get_dashboard_data(date_range \\ :today, options \\ %{}) do
    try do
      # 获取基础仪表板数据
      case UnifiedReportManager.get_dashboard_data(date_range, options[:include_charts] || true) do
        {:ok, dashboard_data} ->
          # 添加额外的仪表板信息
          enhanced_data = enhance_dashboard_data(dashboard_data, options)
          {:ok, enhanced_data}

        {:error, reason} ->
          {:error, reason}
      end
    rescue
      error ->
        Logger.error("获取仪表板数据失败: #{inspect(error)}")
        {:error, "获取仪表板数据异常"}
    end
  end

  @doc """
  获取报表状态概览
  """
  def get_reports_status(date \\ Date.utc_today()) do
    case UnifiedReportManager.get_report_status(date) do
      {:ok, status} ->
        # 添加系统健康状态
        enhanced_status = Map.put(status, :system_health, check_system_health())
        {:ok, enhanced_status}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  导出报表数据
  """
  def export_report(report_type, date, format \\ :csv, options \\ %{}) do
    case UnifiedReportManager.export_report(report_type, date, format, options) do
      {:ok, export_result} ->
        # 记录导出日志
        log_export_activity(report_type, date, format, :success)
        {:ok, export_result}

      {:error, reason} ->
        log_export_activity(report_type, date, format, :failed, reason)
        {:error, reason}
    end
  end

  @doc """
  获取可用报表列表
  """
  def list_available_reports(start_date, end_date, report_types \\ [:system, :channel, :summary, :analytics]) do
    case UnifiedReportManager.list_available_reports(start_date, end_date, report_types) do
      {:ok, reports} ->
        # 按类型和日期排序
        sorted_reports = sort_reports_by_priority(reports)
        {:ok, sorted_reports}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  批量导出报表
  """
  def batch_export_reports(report_configs) do
    Logger.info("开始批量导出报表，配置数量: #{length(report_configs)}")
    
    results = Enum.map(report_configs, fn config ->
      report_type = config[:report_type]
      date = config[:date]
      format = config[:format] || :csv
      options = config[:options] || %{}
      
      case export_report(report_type, date, format, options) do
        {:ok, result} ->
          %{
            report_type: report_type,
            date: date,
            format: format,
            status: :success,
            result: result
          }

        {:error, reason} ->
          %{
            report_type: report_type,
            date: date,
            format: format,
            status: :failed,
            error: reason
          }
      end
    end)

    success_count = Enum.count(results, &(&1.status == :success))
    failed_count = Enum.count(results, &(&1.status == :failed))

    Logger.info("批量导出完成，成功: #{success_count}, 失败: #{failed_count}")

    {:ok, %{
      results: results,
      summary: %{
        total: length(results),
        success: success_count,
        failed: failed_count
      }
    }}
  end

  @doc """
  清理过期报表缓存
  """
  def cleanup_expired_reports(days_to_keep \\ 30) do
    Logger.info("开始清理过期报表缓存，保留天数: #{days_to_keep}")
    
    cutoff_date = Date.add(Date.utc_today(), -days_to_keep)
    
    try do
      # 这里可以实现具体的清理逻辑
      # 例如删除过期的缓存文件、数据库记录等
      
      cleanup_results = %{
        system_reports: cleanup_system_reports(cutoff_date),
        cache_files: cleanup_cache_files(cutoff_date),
        temp_exports: cleanup_temp_exports(cutoff_date)
      }

      Logger.info("报表缓存清理完成: #{inspect(cleanup_results)}")
      {:ok, cleanup_results}
    rescue
      error ->
        Logger.error("清理报表缓存失败: #{inspect(error)}")
        {:error, "清理失败"}
    end
  end

  @doc """
  获取报表性能统计
  """
  def get_performance_stats(days \\ 7) do
    try do
      # 获取最近几天的报表生成性能数据
      stats = %{
        generation_times: get_generation_time_stats(days),
        success_rates: get_success_rate_stats(days),
        resource_usage: get_resource_usage_stats(days),
        error_summary: get_error_summary(days)
      }

      {:ok, stats}
    rescue
      error ->
        Logger.error("获取性能统计失败: #{inspect(error)}")
        {:error, "获取性能统计异常"}
    end
  end

  # ==================== 私有函数 ====================

  defp generate_results_summary(results) do
    %{
      total_reports: length(results),
      successful: Enum.count(results, &match?({:ok, _}, &1)),
      failed: Enum.count(results, &match?({:error, _}, &1)),
      report_types: Enum.map(results, fn
        {:ok, %{report_type: type}} -> type
        _ -> :unknown
      end) |> Enum.uniq()
    }
  end

  defp enhance_dashboard_data(dashboard_data, options) do
    enhanced = dashboard_data
    
    # 添加实时指标
    enhanced = if options[:include_realtime] do
      case SystemSummaryReport.get_realtime_data() do
        {:ok, realtime} -> Map.put(enhanced, :realtime, realtime)
        _ -> enhanced
      end
    else
      enhanced
    end

    # 添加趋势分析
    enhanced = if options[:include_trends] do
      trends = calculate_trends(dashboard_data)
      Map.put(enhanced, :trends, trends)
    else
      enhanced
    end

    enhanced
  end

  defp check_system_health do
    %{
      database_status: check_database_connection(),
      cache_status: check_cache_status(),
      service_status: check_services_status(),
      last_check: DateTime.utc_now()
    }
  end

  defp check_database_connection do
    try do
      # 简单的数据库连接检查
      case Cypridina.Repo.query("SELECT 1") do
        {:ok, _} -> :healthy
        {:error, _} -> :unhealthy
      end
    rescue
      _ -> :unhealthy
    end
  end

  defp check_cache_status do
    # 检查缓存状态
    :healthy
  end

  defp check_services_status do
    # 检查各个服务状态
    %{
      analytics_service: :healthy,
      export_service: :healthy,
      report_manager: :healthy
    }
  end

  defp log_export_activity(report_type, date, format, status, error \\ nil) do
    log_data = %{
      report_type: report_type,
      date: date,
      format: format,
      status: status,
      timestamp: DateTime.utc_now()
    }

    log_data = if error do
      Map.put(log_data, :error, error)
    else
      log_data
    end

    Logger.info("报表导出活动: #{inspect(log_data)}")
  end

  defp sort_reports_by_priority(reports) do
    # 按优先级排序：日期倒序，类型优先级
    type_priority = %{
      system: 1,
      summary: 2,
      analytics: 3,
      channel: 4
    }

    Enum.sort(reports, fn a, b ->
      date_cmp = Date.compare(b.report_date, a.report_date)
      
      if date_cmp == :eq do
        priority_a = Map.get(type_priority, a.report_type, 99)
        priority_b = Map.get(type_priority, b.report_type, 99)
        priority_a <= priority_b
      else
        date_cmp == :gt
      end
    end)
  end

  defp calculate_trends(_dashboard_data) do
    # 计算趋势数据（需要历史数据）
    %{
      user_growth_rate: 0.0,
      revenue_growth_rate: 0.0,
      retention_trend: "stable"
    }
  end

  # 清理相关函数
  defp cleanup_system_reports(cutoff_date) do
    # 清理系统报表
    %{cleaned: 0, errors: 0}
  end

  defp cleanup_cache_files(cutoff_date) do
    # 清理缓存文件
    %{cleaned: 0, errors: 0}
  end

  defp cleanup_temp_exports(cutoff_date) do
    # 清理临时导出文件
    %{cleaned: 0, errors: 0}
  end

  # 性能统计相关函数
  defp get_generation_time_stats(_days) do
    %{avg_time_ms: 1000, max_time_ms: 5000, min_time_ms: 200}
  end

  defp get_success_rate_stats(_days) do
    %{success_rate: 95.5, total_attempts: 100, successful: 95}
  end

  defp get_resource_usage_stats(_days) do
    %{avg_memory_mb: 256, avg_cpu_percent: 15.5}
  end

  defp get_error_summary(_days) do
    %{total_errors: 5, common_errors: ["数据库连接超时", "内存不足"]}
  end
end
