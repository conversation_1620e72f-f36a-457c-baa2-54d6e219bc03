defmodule Teen.Statistics do
  @moduledoc """
  统计分析域

  包含系统报表、在线统计、渠道统计、用户统计、金币统计、留存率统计等功能
  """

  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  alias Teen.Statistics.SystemReport

  admin do
    show? false
  end

  resources do
    resource Teen.Statistics.SystemReport
    resource Teen.Statistics.ChannelReport
    resource Teen.Statistics.SystemSummaryReport
    resource Teen.Statistics.ComprehensiveAnalyticsReport
    resource Teen.Statistics.UnifiedReportManager
  end

  # ==================== 业务逻辑函数 ====================

  @doc """
  生成日报
  """
  def generate_daily_report(report_date \\ Date.utc_today()) do
    SystemReport.generate_daily_report(%{report_date: report_date})
  end

  @doc """
  获取最新报表
  """
  def get_latest_report(report_type \\ "daily") do
    SystemReport.get_latest_report(report_type)
  end

  @doc """
  获取报表数据范围
  """
  def get_reports_by_date_range(start_date, end_date, report_type \\ "daily") do
    with {:ok, reports} <- SystemReport.list_by_date_range(start_date, end_date) do
      filtered_reports = Enum.filter(reports, &(&1.report_type == report_type))
      {:ok, filtered_reports}
    end
  end

  @doc """
  计算统计摘要
  """
  def calculate_statistics_summary(date_range \\ nil) do
    # 这里应该实现实际的统计计算逻辑
    {:ok,
     %{
       total_users: 10000,
       active_users_today: 1500,
       total_revenue: Decimal.new("500000"),
       revenue_today: Decimal.new("15000"),
       game_rounds_today: 5000,
       average_session_time: 45.5
     }}
  end

  # ==================== 统一报表管理函数 ====================

  @doc """
  生成所有类型的报表
  """
  def generate_all_reports(date \\ Date.utc_today(), options \\ %{}) do
    Teen.Services.ReportIntegrationService.generate_all_reports(date, options)
  end

  @doc """
  按类型生成报表
  """
  def generate_report_by_type(report_type, date \\ Date.utc_today(), options \\ %{}) do
    Teen.Services.ReportIntegrationService.generate_report_by_type(report_type, date, options)
  end

  @doc """
  获取报表仪表板数据
  """
  def get_dashboard_data(date_range \\ :today, options \\ %{}) do
    Teen.Services.ReportIntegrationService.get_dashboard_data(date_range, options)
  end

  @doc """
  获取报表状态概览
  """
  def get_reports_status(date \\ Date.utc_today()) do
    Teen.Services.ReportIntegrationService.get_reports_status(date)
  end

  @doc """
  导出报表数据
  """
  def export_report(report_type, date, format \\ :csv, options \\ %{}) do
    Teen.Services.ReportIntegrationService.export_report(report_type, date, format, options)
  end

  @doc """
  获取可用报表列表
  """
  def list_available_reports(start_date, end_date, report_types \\ [:system, :channel, :summary, :analytics]) do
    Teen.Services.ReportIntegrationService.list_available_reports(start_date, end_date, report_types)
  end

  @doc """
  批量导出报表
  """
  def batch_export_reports(report_configs) do
    Teen.Services.ReportIntegrationService.batch_export_reports(report_configs)
  end

  @doc """
  清理过期报表缓存
  """
  def cleanup_expired_reports(days_to_keep \\ 30) do
    Teen.Services.ReportIntegrationService.cleanup_expired_reports(days_to_keep)
  end

  @doc """
  获取报表性能统计
  """
  def get_performance_stats(days \\ 7) do
    Teen.Services.ReportIntegrationService.get_performance_stats(days)
  end

  # ==================== 综合分析函数 ====================

  @doc """
  获取综合分析数据
  """
  def get_comprehensive_analytics(date_range \\ :today, options \\ %{}) do
    Teen.Services.ComprehensiveAnalyticsService.get_comprehensive_analytics(date_range, options)
  end

  @doc """
  导出分析数据为CSV
  """
  def export_analytics_csv(start_date, end_date, options \\ %{}) do
    Teen.Services.AnalyticsExportService.export_to_csv(start_date, end_date, options)
  end

  @doc """
  生成图表数据
  """
  def generate_chart_data(chart_type, start_date, end_date, metrics \\ []) do
    Teen.Services.AnalyticsExportService.generate_chart_data(chart_type, start_date, end_date, metrics)
  end

  @doc """
  生成报表摘要
  """
  def generate_report_summary(start_date, end_date) do
    Teen.Services.AnalyticsExportService.generate_report_summary(start_date, end_date)
  end
end
