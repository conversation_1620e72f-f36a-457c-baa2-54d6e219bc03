defmodule CypridinaWeb.Admin.ReportsController do
  @moduledoc """
  管理后台报表控制器

  提供报表管理的Web界面，包括：
  - 报表生成和查看
  - 报表导出功能
  - 报表状态监控
  - 报表性能统计
  """

  use <PERSON><PERSON><PERSON>ina<PERSON>eb, :controller

  alias Teen.Statistics
  alias Teen.Services.ReportIntegrationService

  @doc """
  报表管理首页
  """
  def index(conn, _params) do
    # 获取报表状态概览
    case Statistics.get_reports_status() do
      {:ok, status} ->
        # 获取最近的报表列表
        end_date = Date.utc_today()
        start_date = Date.add(end_date, -7)
        
        case Statistics.list_available_reports(start_date, end_date) do
          {:ok, reports} ->
            conn
            |> assign(:status, status)
            |> assign(:recent_reports, reports)
            |> assign(:page_title, "报表管理")
            |> render(:index)

          {:error, _reason} ->
            conn
            |> assign(:status, status)
            |> assign(:recent_reports, [])
            |> assign(:page_title, "报表管理")
            |> render(:index)
        end

      {:error, _reason} ->
        conn
        |> put_flash(:error, "获取报表状态失败")
        |> assign(:status, %{})
        |> assign(:recent_reports, [])
        |> assign(:page_title, "报表管理")
        |> render(:index)
    end
  end

  @doc """
  生成报表
  """
  def generate(conn, params) do
    report_type = String.to_atom(params["report_type"] || "system")
    report_date = parse_date(params["report_date"])
    force_regenerate = params["force_regenerate"] == "true"

    options = %{
      force_regenerate: force_regenerate,
      include_details: params["include_details"] == "true"
    }

    case Statistics.generate_report_by_type(report_type, report_date, options) do
      {:ok, result} ->
        conn
        |> put_flash(:info, "报表生成成功")
        |> json(%{
          success: true,
          data: result,
          message: "报表生成完成"
        })

      {:error, reason} ->
        conn
        |> put_flash(:error, "报表生成失败: #{reason}")
        |> json(%{
          success: false,
          error: reason,
          message: "报表生成失败"
        })
    end
  end

  @doc """
  批量生成报表
  """
  def batch_generate(conn, params) do
    report_date = parse_date(params["report_date"])
    force_regenerate = params["force_regenerate"] == "true"

    options = %{
      force_regenerate: force_regenerate,
      include_details: params["include_details"] == "true"
    }

    case Statistics.generate_all_reports(report_date, options) do
      {:ok, result} ->
        conn
        |> put_flash(:info, "批量报表生成成功")
        |> json(%{
          success: true,
          data: result,
          message: "所有报表生成完成"
        })

      {:error, reason} ->
        conn
        |> put_flash(:error, "批量报表生成失败: #{reason}")
        |> json(%{
          success: false,
          error: reason,
          message: "批量报表生成失败"
        })
    end
  end

  @doc """
  导出报表
  """
  def export(conn, params) do
    report_type = String.to_atom(params["report_type"] || "system")
    report_date = parse_date(params["report_date"])
    export_format = String.to_atom(params["format"] || "csv")

    export_options = %{
      fields: params["fields"] || [],
      include_metadata: params["include_metadata"] == "true"
    }

    case Statistics.export_report(report_type, report_date, export_format, export_options) do
      {:ok, export_result} ->
        filename = export_result.export_data[:filename] || "report.#{export_format}"
        content = export_result.export_data[:content] || ""
        content_type = get_content_type(export_format)

        conn
        |> put_resp_content_type(content_type)
        |> put_resp_header("content-disposition", "attachment; filename=\"#{filename}\"")
        |> send_resp(200, content)

      {:error, reason} ->
        conn
        |> put_flash(:error, "导出失败: #{reason}")
        |> redirect(to: ~p"/admin/reports")
    end
  end

  @doc """
  批量导出报表
  """
  def batch_export(conn, params) do
    report_configs = parse_export_configs(params["configs"] || "[]")

    case Statistics.batch_export_reports(report_configs) do
      {:ok, result} ->
        conn
        |> put_flash(:info, "批量导出完成，成功: #{result.summary.success}, 失败: #{result.summary.failed}")
        |> json(%{
          success: true,
          data: result,
          message: "批量导出完成"
        })

      {:error, reason} ->
        conn
        |> put_flash(:error, "批量导出失败: #{reason}")
        |> json(%{
          success: false,
          error: reason,
          message: "批量导出失败"
        })
    end
  end

  @doc """
  获取报表状态
  """
  def status(conn, params) do
    report_date = parse_date(params["report_date"])

    case Statistics.get_reports_status(report_date) do
      {:ok, status} ->
        conn
        |> json(%{
          success: true,
          data: status,
          message: "获取状态成功"
        })

      {:error, reason} ->
        conn
        |> json(%{
          success: false,
          error: reason,
          message: "获取状态失败"
        })
    end
  end

  @doc """
  获取性能统计
  """
  def performance(conn, params) do
    days = String.to_integer(params["days"] || "7")

    case Statistics.get_performance_stats(days) do
      {:ok, stats} ->
        conn
        |> json(%{
          success: true,
          data: stats,
          message: "获取性能统计成功"
        })

      {:error, reason} ->
        conn
        |> json(%{
          success: false,
          error: reason,
          message: "获取性能统计失败"
        })
    end
  end

  @doc """
  清理过期报表
  """
  def cleanup(conn, params) do
    days_to_keep = String.to_integer(params["days_to_keep"] || "30")

    case Statistics.cleanup_expired_reports(days_to_keep) do
      {:ok, cleanup_result} ->
        conn
        |> put_flash(:info, "清理完成")
        |> json(%{
          success: true,
          data: cleanup_result,
          message: "清理过期报表完成"
        })

      {:error, reason} ->
        conn
        |> put_flash(:error, "清理失败: #{reason}")
        |> json(%{
          success: false,
          error: reason,
          message: "清理过期报表失败"
        })
    end
  end

  @doc """
  获取仪表板数据
  """
  def dashboard(conn, params) do
    date_range = String.to_atom(params["date_range"] || "today")
    
    options = %{
      include_charts: params["include_charts"] != "false",
      include_realtime: params["include_realtime"] == "true",
      include_trends: params["include_trends"] == "true"
    }

    case Statistics.get_dashboard_data(date_range, options) do
      {:ok, dashboard_data} ->
        conn
        |> json(%{
          success: true,
          data: dashboard_data,
          message: "获取仪表板数据成功"
        })

      {:error, reason} ->
        conn
        |> json(%{
          success: false,
          error: reason,
          message: "获取仪表板数据失败"
        })
    end
  end

  # ==================== 私有函数 ====================

  defp parse_date(nil), do: Date.utc_today()
  defp parse_date(date_string) when is_binary(date_string) do
    case Date.from_iso8601(date_string) do
      {:ok, date} -> date
      {:error, _} -> Date.utc_today()
    end
  end
  defp parse_date(%Date{} = date), do: date
  defp parse_date(_), do: Date.utc_today()

  defp parse_export_configs(configs_json) do
    case Jason.decode(configs_json) do
      {:ok, configs} when is_list(configs) ->
        Enum.map(configs, fn config ->
          %{
            report_type: String.to_atom(config["report_type"] || "system"),
            date: parse_date(config["date"]),
            format: String.to_atom(config["format"] || "csv"),
            options: config["options"] || %{}
          }
        end)

      _ ->
        []
    end
  end

  defp get_content_type(:csv), do: "text/csv"
  defp get_content_type(:excel), do: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  defp get_content_type(:json), do: "application/json"
  defp get_content_type(_), do: "application/octet-stream"
end
