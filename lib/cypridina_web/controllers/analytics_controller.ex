defmodule CypridinaWeb.AnalyticsController do
  @moduledoc """
  分析数据控制器

  提供分析数据的API接口，包括：
  - 获取综合分析数据
  - 导出数据
  - 生成图表数据
  - 获取报表摘要
  """

  use <PERSON><PERSON>ridinaWeb, :controller

  alias Teen.Statistics.SystemSummaryReport

  @doc """
  获取综合指标数据
  """
  def get_comprehensive_metrics(conn, params) do
    report_date = parse_date(params["report_date"])

    case SystemSummaryReport.get_comprehensive_metrics(report_date) do
      {:ok, metrics_data} ->
        conn
        |> put_status(:ok)
        |> json(%{
          success: true,
          data: metrics_data,
          message: "获取综合指标成功"
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: reason,
          message: "获取综合指标失败"
        })
    end
  end

  @doc """
  导出报表数据
  """
  def export_report(conn, params) do
    start_date = parse_date(params["start_date"])
    end_date = parse_date(params["end_date"])
    format = String.to_atom(params["format"] || "csv")

    case SystemSummaryReport.export_report_data(start_date, end_date, format) do
      {:ok, export_data} ->
        conn
        |> put_resp_content_type(export_data.content_type)
        |> put_resp_header("content-disposition", "attachment; filename=\"#{export_data.filename}\"")
        |> send_resp(200, export_data.content)

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: reason,
          message: "导出失败"
        })
    end
  end

  @doc """
  获取图表数据
  """
  def get_chart_data(conn, params) do
    chart_type = String.to_atom(params["chart_type"] || "line")
    start_date = parse_date(params["start_date"])
    end_date = parse_date(params["end_date"])
    metrics = parse_metrics(params["metrics"] || "")

    case SystemSummaryReport.get_chart_data(chart_type, start_date, end_date, metrics) do
      {:ok, chart_data} ->
        conn
        |> put_status(:ok)
        |> json(%{
          success: true,
          data: chart_data,
          message: "获取图表数据成功"
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: reason,
          message: "获取图表数据失败"
        })
    end
  end

  @doc """
  获取留存分析数据
  """
  def get_retention_analysis(conn, params) do
    cohort_date = parse_date(params["cohort_date"])

    case SystemSummaryReport.get_retention_analysis(cohort_date) do
      {:ok, retention_data} ->
        conn
        |> put_status(:ok)
        |> json(%{
          success: true,
          data: retention_data,
          message: "获取留存分析成功"
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: reason,
          message: "获取留存分析失败"
        })
    end
  end

  @doc """
  获取实时数据
  """
  def get_realtime_data(conn, _params) do
    case SystemSummaryReport.get_realtime_data() do
      {:ok, realtime_data} ->
        conn
        |> put_status(:ok)
        |> json(%{
          success: true,
          data: realtime_data,
          message: "获取实时数据成功"
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: reason,
          message: "获取实时数据失败"
        })
    end
  end

  @doc """
  获取在线统计
  """
  def get_online_statistics(conn, _params) do
    case SystemSummaryReport.get_online_statistics() do
      {:ok, online_stats} ->
        conn
        |> put_status(:ok)
        |> json(%{
          success: true,
          data: online_stats,
          message: "获取在线统计成功"
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: reason,
          message: "获取在线统计失败"
        })
    end
  end

  @doc """
  生成系统日报
  """
  def generate_daily_report(conn, params) do
    report_date = parse_date(params["report_date"])

    case SystemSummaryReport.generate_system_daily_report(report_date) do
      {:ok, report} ->
        conn
        |> put_status(:ok)
        |> json(%{
          success: true,
          data: report,
          message: "生成日报成功"
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: reason,
          message: "生成日报失败"
        })
    end
  end

  @doc """
  生成系统月报
  """
  def generate_monthly_report(conn, params) do
    year = String.to_integer(params["year"] || to_string(Date.utc_today().year))
    month = String.to_integer(params["month"] || to_string(Date.utc_today().month))

    case SystemSummaryReport.generate_system_monthly_report(year, month) do
      {:ok, report} ->
        conn
        |> put_status(:ok)
        |> json(%{
          success: true,
          data: report,
          message: "生成月报成功"
        })

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: reason,
          message: "生成月报失败"
        })
    end
  end

  # ==================== 私有函数 ====================

  defp parse_date(nil), do: Date.utc_today()
  defp parse_date(date_string) when is_binary(date_string) do
    case Date.from_iso8601(date_string) do
      {:ok, date} -> date
      {:error, _} -> Date.utc_today()
    end
  end
  defp parse_date(%Date{} = date), do: date
  defp parse_date(_), do: Date.utc_today()

  defp parse_metrics(""), do: []
  defp parse_metrics(metrics_string) when is_binary(metrics_string) do
    metrics_string
    |> String.split(",")
    |> Enum.map(&String.trim/1)
    |> Enum.filter(&(&1 != ""))
    |> Enum.map(&String.to_atom/1)
  end
  defp parse_metrics(metrics) when is_list(metrics) do
    Enum.map(metrics, fn
      metric when is_binary(metric) -> String.to_atom(metric)
      metric when is_atom(metric) -> metric
      _ -> nil
    end)
    |> Enum.filter(&(&1 != nil))
  end
  defp parse_metrics(_), do: []
end
