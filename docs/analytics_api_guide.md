# 分析数据API使用指南

本文档介绍如何使用分析数据API来获取各种报表统计信息。

## API端点概览

### 基础URL
```
http://localhost:4000/api/analytics
```

## 主要功能

### 1. 获取综合指标数据

**端点**: `GET /api/analytics/metrics`

**参数**:
- `report_date` (可选): 报表日期，格式 YYYY-MM-DD，默认为今天

**响应示例**:
```json
{
  "success": true,
  "data": {
    "report_date": "2024-01-01",
    "report_type": "comprehensive",
    "new_devices": 50,
    "new_registrations": 100,
    "valid_new_users": 80,
    "mobile_bound_registrations": 60,
    "active_users": 500,
    "valid_active_users": 400,
    "active_mobile_bound": 300,
    "online_users": 150,
    "paying_users": 125,
    "new_paying_users": 25,
    "first_time_payers": 20,
    "total_recharge_amount": "50000.00",
    "new_recharge_amount": "10000.00",
    "withdrawal_amount": "15000.00",
    "withdrawal_users": 30,
    "withdrawal_recharge_ratio": "30.00",
    "payment_rate": "25.00",
    "new_payment_rate": "25.00",
    "valid_payment_rate": "31.25",
    "valid_new_payment_rate": "31.25",
    "arpu": "100.00",
    "arppu": "400.00",
    "valid_arpu": "125.00",
    "next_day_retention": "65.00",
    "day3_retention": "45.00",
    "day7_retention": "30.00",
    "day14_retention": "20.00",
    "day30_retention": "15.00",
    "total_games_played": 5000,
    "games_played_today": 500,
    "avg_session_time": "45.5"
  },
  "message": "获取综合指标成功"
}
```

### 2. 导出报表数据

**端点**: `GET /api/analytics/export`

**参数**:
- `start_date` (必需): 开始日期，格式 YYYY-MM-DD
- `end_date` (必需): 结束日期，格式 YYYY-MM-DD
- `format` (可选): 导出格式，支持 csv、json、excel，默认为 csv

**使用示例**:
```bash
# 导出CSV格式
curl "http://localhost:4000/api/analytics/export?start_date=2024-01-01&end_date=2024-01-07&format=csv" \
  -H "Accept: text/csv" \
  -o analytics_report.csv

# 导出JSON格式
curl "http://localhost:4000/api/analytics/export?start_date=2024-01-01&end_date=2024-01-07&format=json" \
  -H "Accept: application/json" \
  -o analytics_report.json
```

### 3. 获取图表数据

**端点**: `GET /api/analytics/charts`

**参数**:
- `chart_type` (必需): 图表类型，支持 line、bar、pie、area、funnel
- `start_date` (必需): 开始日期
- `end_date` (必需): 结束日期
- `metrics` (可选): 指标列表，用逗号分隔

**支持的指标**:
- `new_devices`: 新增设备
- `new_registrations`: 新增注册
- `valid_new_users`: 有效新增
- `mobile_bound_registrations`: 手机注册绑定
- `active_users`: 活跃用户
- `valid_active_users`: 有效活跃
- `active_mobile_bound`: 活跃手机绑定
- `paying_users`: 充值人数
- `new_paying_users`: 新增充值人数
- `first_time_payers`: 首付人数
- `total_recharge_amount`: 总充值额
- `new_recharge_amount`: 新增充值额
- `withdrawal_amount`: 退出总额
- `withdrawal_users`: 退出人数
- `withdrawal_recharge_ratio`: 退充比
- `payment_rate`: 付费率
- `new_payment_rate`: 新增付费率
- `valid_payment_rate`: 有效付费率
- `valid_new_payment_rate`: 有效新增付费率
- `arpu`: ARPU
- `arppu`: ARPPU
- `valid_arpu`: 有效ARPU
- `next_day_retention`: 次留
- `day3_retention`: 3留
- `day7_retention`: 7留
- `day14_retention`: 14留
- `day30_retention`: 30留

**使用示例**:
```bash
# 获取折线图数据
curl "http://localhost:4000/api/analytics/charts?chart_type=line&start_date=2024-01-01&end_date=2024-01-07&metrics=new_registrations,active_users,paying_users"

# 获取柱状图数据
curl "http://localhost:4000/api/analytics/charts?chart_type=bar&start_date=2024-01-01&end_date=2024-01-07&metrics=arpu,arppu"

# 获取饼图数据
curl "http://localhost:4000/api/analytics/charts?chart_type=pie&start_date=2024-01-07&end_date=2024-01-07&metrics=new_registrations,valid_new_users,paying_users"
```

### 4. 获取留存分析数据

**端点**: `GET /api/analytics/retention`

**参数**:
- `cohort_date` (必需): 队列日期，格式 YYYY-MM-DD

**响应示例**:
```json
{
  "success": true,
  "data": {
    "cohort_date": "2024-01-01",
    "cohort_size": 100,
    "retention_data": [
      {
        "day": 1,
        "retained_users": 65,
        "retention_rate": 65.0,
        "cohort_size": 100
      },
      {
        "day": 3,
        "retained_users": 45,
        "retention_rate": 45.0,
        "cohort_size": 100
      },
      {
        "day": 7,
        "retained_users": 30,
        "retention_rate": 30.0,
        "cohort_size": 100
      },
      {
        "day": 14,
        "retained_users": 20,
        "retention_rate": 20.0,
        "cohort_size": 100
      },
      {
        "day": 30,
        "retained_users": 15,
        "retention_rate": 15.0,
        "cohort_size": 100
      }
    ],
    "analysis_date": "2024-01-31"
  },
  "message": "获取留存分析成功"
}
```

### 5. 获取实时数据

**端点**: `GET /api/analytics/realtime`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "report_date": "2024-01-01",
    "report_type": "realtime",
    "online_users": 150,
    "new_registrations": 25,
    "new_recharge_amount": "5000.00",
    "games_played_today": 500,
    "total_games_played": 5000,
    "active_users": 120,
    "report_data": {
      "timestamp": "2024-01-01T12:00:00Z",
      "type": "realtime_data",
      "last_update": "2024-01-01T12:00:00Z"
    }
  },
  "message": "获取实时数据成功"
}
```

### 6. 获取在线统计

**端点**: `GET /api/analytics/online`

### 7. 生成系统日报

**端点**: `POST /api/analytics/daily-report`

**参数**:
- `report_date` (可选): 报表日期，默认为今天

### 8. 生成系统月报

**端点**: `POST /api/analytics/monthly-report`

**参数**:
- `year` (可选): 年份，默认为当前年份
- `month` (可选): 月份，默认为当前月份

## 错误处理

所有API在出错时都会返回以下格式的错误响应：

```json
{
  "success": false,
  "error": "错误详情",
  "message": "用户友好的错误消息"
}
```

## 使用建议

1. **数据缓存**: 综合指标数据计算较为复杂，建议在前端进行适当缓存
2. **分页处理**: 对于大量数据的导出，建议分批处理
3. **图表优化**: 根据数据量选择合适的图表类型和时间范围
4. **留存分析**: 留存分析需要足够的历史数据，建议至少等待30天后再进行分析

## 前端集成示例

### JavaScript/React示例

```javascript
// 获取综合指标数据
const fetchMetrics = async (reportDate) => {
  try {
    const response = await fetch(`/api/analytics/metrics?report_date=${reportDate}`);
    const data = await response.json();
    
    if (data.success) {
      return data.data;
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    console.error('获取指标数据失败:', error);
    throw error;
  }
};

// 获取图表数据
const fetchChartData = async (chartType, startDate, endDate, metrics) => {
  const metricsParam = metrics.join(',');
  const url = `/api/analytics/charts?chart_type=${chartType}&start_date=${startDate}&end_date=${endDate}&metrics=${metricsParam}`;
  
  try {
    const response = await fetch(url);
    const data = await response.json();
    
    if (data.success) {
      return data.data;
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    console.error('获取图表数据失败:', error);
    throw error;
  }
};

// 导出数据
const exportData = async (startDate, endDate, format = 'csv') => {
  const url = `/api/analytics/export?start_date=${startDate}&end_date=${endDate}&format=${format}`;
  
  try {
    const response = await fetch(url);
    
    if (response.ok) {
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `analytics_report_${startDate}_${endDate}.${format}`;
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(downloadUrl);
    } else {
      throw new Error('导出失败');
    }
  } catch (error) {
    console.error('导出数据失败:', error);
    throw error;
  }
};
```

这个API系统提供了完整的分析数据功能，支持各种统计指标的查询、导出和可视化展示。
