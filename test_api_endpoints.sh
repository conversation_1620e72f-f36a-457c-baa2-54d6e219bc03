#!/bin/bash

# 分析API端点测试脚本
# 用于测试所有分析相关的API端点

BASE_URL="http://localhost:4000"
API_BASE="${BASE_URL}/api/analytics"

echo "=== 分析API端点测试 ==="
echo "基础URL: ${API_BASE}"
echo ""

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_endpoint() {
    local method=$1
    local endpoint=$2
    local description=$3
    local expected_status=${4:-200}
    
    echo -e "${YELLOW}测试: ${description}${NC}"
    echo "请求: ${method} ${endpoint}"
    
    if command -v curl >/dev/null 2>&1; then
        response=$(curl -s -w "\n%{http_code}" -X "${method}" "${endpoint}" -H "Accept: application/json" 2>/dev/null)
        
        if [ $? -eq 0 ]; then
            # 分离响应体和状态码
            status_code=$(echo "$response" | tail -n1)
            response_body=$(echo "$response" | head -n -1)
            
            if [ "$status_code" -eq "$expected_status" ]; then
                echo -e "${GREEN}✅ 成功 (状态码: ${status_code})${NC}"
                
                # 尝试解析JSON响应
                if echo "$response_body" | python3 -m json.tool >/dev/null 2>&1; then
                    echo "响应格式: 有效的JSON"
                    # 显示响应的前几行
                    echo "$response_body" | python3 -m json.tool | head -10
                    if [ $(echo "$response_body" | python3 -m json.tool | wc -l) -gt 10 ]; then
                        echo "... (响应内容已截断)"
                    fi
                else
                    echo "响应格式: 非JSON或其他格式"
                    echo "$response_body" | head -5
                fi
            else
                echo -e "${RED}❌ 失败 (期望状态码: ${expected_status}, 实际: ${status_code})${NC}"
                echo "响应内容:"
                echo "$response_body" | head -5
            fi
        else
            echo -e "${RED}❌ 请求失败${NC}"
        fi
    else
        echo -e "${RED}❌ curl 命令不可用${NC}"
    fi
    
    echo ""
}

# 检查服务器是否运行
echo "检查服务器状态..."
if curl -s "${BASE_URL}" >/dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务器正在运行${NC}"
else
    echo -e "${RED}❌ 服务器未运行或无法访问${NC}"
    echo "请确保Phoenix服务器在 ${BASE_URL} 上运行"
    exit 1
fi
echo ""

# 测试各个端点
echo "开始测试API端点..."
echo ""

# 1. 获取综合指标数据
test_endpoint "GET" "${API_BASE}/metrics" "获取综合指标数据"

# 2. 获取综合指标数据（指定日期）
today=$(date +%Y-%m-%d)
test_endpoint "GET" "${API_BASE}/metrics?report_date=${today}" "获取指定日期的综合指标数据"

# 3. 获取实时数据
test_endpoint "GET" "${API_BASE}/realtime" "获取实时数据"

# 4. 获取在线统计
test_endpoint "GET" "${API_BASE}/online" "获取在线统计"

# 5. 获取图表数据 - 折线图
start_date=$(date -d '7 days ago' +%Y-%m-%d)
end_date=$(date +%Y-%m-%d)
metrics="new_registrations,active_users,paying_users"
test_endpoint "GET" "${API_BASE}/charts?chart_type=line&start_date=${start_date}&end_date=${end_date}&metrics=${metrics}" "获取折线图数据"

# 6. 获取图表数据 - 柱状图
test_endpoint "GET" "${API_BASE}/charts?chart_type=bar&start_date=${start_date}&end_date=${end_date}&metrics=arpu,arppu" "获取柱状图数据"

# 7. 获取图表数据 - 饼图
test_endpoint "GET" "${API_BASE}/charts?chart_type=pie&start_date=${today}&end_date=${today}&metrics=new_registrations,valid_new_users,paying_users" "获取饼图数据"

# 8. 获取留存分析数据
cohort_date=$(date -d '7 days ago' +%Y-%m-%d)
test_endpoint "GET" "${API_BASE}/retention?cohort_date=${cohort_date}" "获取留存分析数据"

# 9. 导出CSV数据
echo -e "${YELLOW}测试: 导出CSV数据${NC}"
echo "请求: GET ${API_BASE}/export?start_date=${start_date}&end_date=${end_date}&format=csv"
if command -v curl >/dev/null 2>&1; then
    response=$(curl -s -w "\n%{http_code}" "${API_BASE}/export?start_date=${start_date}&end_date=${end_date}&format=csv" -H "Accept: text/csv" 2>/dev/null)
    status_code=$(echo "$response" | tail -n1)
    
    if [ "$status_code" -eq 200 ]; then
        echo -e "${GREEN}✅ CSV导出成功${NC}"
        response_body=$(echo "$response" | head -n -1)
        echo "CSV内容预览:"
        echo "$response_body" | head -3
        echo "... (内容已截断)"
    else
        echo -e "${RED}❌ CSV导出失败 (状态码: ${status_code})${NC}"
    fi
else
    echo -e "${RED}❌ curl 命令不可用${NC}"
fi
echo ""

# 10. 导出JSON数据
echo -e "${YELLOW}测试: 导出JSON数据${NC}"
echo "请求: GET ${API_BASE}/export?start_date=${start_date}&end_date=${end_date}&format=json"
if command -v curl >/dev/null 2>&1; then
    response=$(curl -s -w "\n%{http_code}" "${API_BASE}/export?start_date=${start_date}&end_date=${end_date}&format=json" -H "Accept: application/json" 2>/dev/null)
    status_code=$(echo "$response" | tail -n1)
    
    if [ "$status_code" -eq 200 ]; then
        echo -e "${GREEN}✅ JSON导出成功${NC}"
        response_body=$(echo "$response" | head -n -1)
        if echo "$response_body" | python3 -m json.tool >/dev/null 2>&1; then
            echo "JSON格式验证: 有效"
            echo "JSON内容预览:"
            echo "$response_body" | python3 -m json.tool | head -5
            echo "... (内容已截断)"
        else
            echo "JSON格式验证: 无效"
        fi
    else
        echo -e "${RED}❌ JSON导出失败 (状态码: ${status_code})${NC}"
    fi
else
    echo -e "${RED}❌ curl 命令不可用${NC}"
fi
echo ""

# 11. 生成系统日报
test_endpoint "POST" "${API_BASE}/daily-report" "生成系统日报"

# 12. 生成系统日报（指定日期）
test_endpoint "POST" "${API_BASE}/daily-report" "生成指定日期的系统日报" 200

# 13. 生成系统月报
current_year=$(date +%Y)
current_month=$(date +%m)
test_endpoint "POST" "${API_BASE}/monthly-report?year=${current_year}&month=${current_month}" "生成系统月报"

echo "=== 测试完成 ==="
echo ""
echo "说明:"
echo "- ✅ 表示端点响应正常"
echo "- ❌ 表示端点响应异常或服务器错误"
echo "- 某些端点可能因为数据库连接或数据不存在而返回错误，这是正常的"
echo "- 在生产环境中，请确保所有依赖的数据表和模块都已正确配置"
echo ""
echo "如需详细调试，可以单独测试每个端点："
echo "curl -v \"${API_BASE}/metrics\" -H \"Accept: application/json\""
